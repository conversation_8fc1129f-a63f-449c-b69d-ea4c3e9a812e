<section>
  {{#each data}}
    {{#if (eq key "associated_parties.associated_parties_call")}}
      {{#each questions}}
        {{#if questions}}
        {{> ODD-Diff-Header timestamp=../../timestamp templateName=../../templateName jNumber=../../jNumber taskId=../../taskId}}
        <table>
          <tr>
            <td class="title" colspan="6">
              {{../../section}}
            </td>
          </tr>
          {{> ODD-Diff-SectionHeader}}
          {{> ODD-Diff-Section}}
        </table>
        <div class="page-break"></div>
        {{/if}}
      {{/each}}
    {{/if}}
  {{/each}}
  {{> ODD-Diff-Header timestamp=timestamp templateName=templateName jNumber=jNumber taskId=taskId}}
  <table>
    <tr>
      <td class="title" colspan="6">
        {{section}}
      </td>
    </tr>
    {{> ODD-Diff-SectionHeader}}
    {{#each data}}
      {{#unless (eq key "associated_parties.associated_parties_call")}}
        {{> ODD-Diff-Section}}
      {{/unless}}
    {{/each}}
  </table>
</section>
