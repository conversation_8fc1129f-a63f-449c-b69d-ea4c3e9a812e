<style type="text/css">
  * {
    font-family: sans-serif;
    line-height: 1.5;
  }

  .centered {
    text-align: center;
  }

  .bold {
    font-weight: bold;
  }

  .tab {
    padding-left: 30px;
  }

  .timestamp {
    text-align: left;
  }

  header {
    width: 100%;
    position: relative;
    padding-top: 5px;
    color: rgb(60, 132, 197);
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 40px;

    .title {
      float: left;
      width: 40%;
      text-align: left;
    }

    .num {
      float: left;
      width: 30%;
      text-align: center;
    }

    .id {
      float: right;
      width: 30%;
      text-align: right;
    }
  }

  table {
    font-size: 12px;
    min-width: 100%;
    background-color: #eeeeee;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
    margin-bottom: 50px;

    tr {
      page-break-inside: avoid;

      td {
        border: 2px solid white;
        padding: 5px;

        &.title {
          background-color: rgb(60, 132, 197);
          color: white;
          font-size: 16px;
          font-weight: bold;
        }

        &.header {
          background-color: rgb(60, 132, 197);
          font-weight: bold;
          color: white;

          &.source {
            width: 10%;
          }
        }

        &.value {
          word-break: break-word; 
          &.Yes {
            color: red;
            font-weight: bold;
          }
        }
      }
    }
  }

</style>
