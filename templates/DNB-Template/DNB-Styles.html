<style type="text/css">
  body {
    font-family: sans-serif;
  }

  header {
    width: 100%;
    line-height: 18px;
    font-size: 12px;
    position: relative;
    padding-top: 5px;
    text-align: center;
    padding-bottom: 10px;
  }

  table, th, td {
    border-collapse: collapse;
  }

  thead {
    background-color: #d5dce4;
  }

  th, td {
    border: 1px solid black;
    font-size: 11px;
    padding: 5px;
  }

  td {
    overflow-wrap: break-word;
  }

  .full-width {
    width: 100%;
  }

  .customerInfo {
    table {
      width: 100%;
      margin-bottom: 30px;

      td {
        border: 1px solid black;
        font-size: 10.6px;
        padding-left: 5px;
        text-align: left;
      }

      tr {
        height: 37.66px;
      }

      .titleColumn {
        width: 146.66px;  
        background-color: #d5dce4;
        vertical-align: middle;
        font-weight: 700;
      }

      .titleColumnFirst {
        width: 146.66;  
        background-color: #d5dce4;
        vertical-align: middle;
        font-weight: 700;
      }

      .valueColumn {
        min-width: 200px;
        vertical-align: middle;
      }
    }
    .empty {
      border: none;
      min-width: 200px;
    }
  }

  .familyTree {
    table {
      margin-bottom: 50px;
    }

    .tableTitle {
      margin-bottom: 5px;
      text-decoration: underline;
    }
  }
</style>