  <div class="basicTableHeader">
    <h6>Accounts and Product Details</h6>
  </div>
  <table class="conditionalTable">
    <tr class="headers">
        <th class="firstColumn">Product type</th>
        <th class="firstColumn">Product Name</th>
        <th>Account Name</th>
        <th>Account Number</th>
        <th>Sort Code</th>
        <th>TAD Turnover Summary</th>
    </tr>
    {{#each accounts_product_types}}
        {{#if (and (ne @index 0) (mod @index 38 ../mod_offset))}}
            </table>
            <div class="pageFooter" />
  </div>
  </div>
  <div class="page">
  <div class="pageHeader">
    <div class="tadText">
        {{../tad_template}} for {{../j_number}} up to {{../last_transaction_date}}
    </div>
  </div>
            <table class="conditionalTable">
                <tr class="emptyRow">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr class="emptyRow">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr class="emptyRow">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr class="headers">
                    <th class="firstColumn">Product type</th>
                    <th class="firstColumn">Product Name</th>
                    <th>Account Name</th>
                    <th>Account Number</th>
                    <th>Sort Code</th>
                    <th>TAD Turnover Scope</th>
                </tr>
        {{/if}}
        <tr class="conditionalTableRow">
            <td class="firstColumn">{{product_type}}</td>
            <td>{{product_name}}</td>
            <td>{{account_name}}</td>
            <td>{{account_number}}</td>
            <td>{{sort_code}}</td>
            <td>{{tad_turnover_scope}}</td>
        </tr>
    {{/each}}
  </table>
</div>