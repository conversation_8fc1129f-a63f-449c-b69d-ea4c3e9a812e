<STYLE type="text/css">
.body {
    background-color: white;
    font-family: sans-serif;
    counter-reset: page;
}

.page {
    padding-left: 2px;
    padding-right: 2px;
    margin-top: 5px;
}

h5 {
    line-height: 10px;
}

.title {
    line-height: 0px;
    padding-left: 7px;
}

.titleTable {
    margin-top: -5px;
    width: 250px;
    text-align: left;
    background-color: rgb(60, 132, 197);
    color: white;
    height: 20px;
    font-weight: 700;
    position: absolute;
}


.column {
    float: left;
    width: 48%;
    padding: 5px;
  }
  
.row::after {
    content: "";
    clear: both;
    display: table;
}

table {
    width: 100%;
    margin-bottom: 10px;
    background-color: #eeeeee;
    text-align: right;
    border-collapse: collapse;
    box-sizing: border-box;

    .headers {
        background-color: rgb(60, 132, 197);
        color: white;

        .topHeader{
            text-align: center;
        }
    }

    .tableHeaderColumn {
        color: rgb(60, 132, 197);
        font-size: 13px;
        font-weight: 700;
        background-color: white;
        text-align: left;
    }

    .yearOnYear {
        font-style: italic;
        padding-right: 5px;
    }

    .percentValue {
        font-style: italic;
    }
}

.phrColumn {
    background-color: #C00000;
    color: white;
    padding-left: 5px;
    text-align: left;
}

.hrColumn {
    background-color: #ED7D31;
    color: white;
    text-align: left;
    padding-left: 5px;
}

.mlColumn {
    background-color: #00B050;
    color: white;
    text-align: left;
    padding-left: 5px;
}

.emptyRow {
    height: 8px;
    background-color: white;
}

.firstTable {
    margin-top: 25px;
    position: relative;
    margin-bottom: 2px;
}

.secondTable {
    margin-bottom: 2px;
}

.monthly {
    margin-bottom: 30px;
}

.conditionalTableRow {
    height: 12px;
}

.conditionalTable {
    padding-left: 7px;
    padding-right: 7px;
    margin-bottom: 25px;
    .headers {
        height: 15px;
    }
}

.riskTableFinal {
    line-height: 10px;
}

.firstColumn {
    text-align: left;
    padding-left: 5px;
}

.firstColumnPercent {
    text-align: left;
    font-style: italic;
    padding-left: 5px;
} 

.lowRisk{
    text-align: right;
    font-weight: bold;
    .lowRiskHeader {
        background-color: #00B050;
        color: white;
        text-align: left;
        padding-left: 5px;
    }
}

.highRisk{
    text-align: right;
    font-weight: bold;
    .highRiskHeader {
        background-color: #ED7D31;
        color: white;
        text-align: left;
        padding-left: 5px;
    }
}

.highRiskThird{
    text-align: right;
    font-weight: bold;
    .highRiskThirdHeader {
        background-color: #C00000;
        color: white;
        padding-left: 5px;
        text-align: left;
    }
}

.totalRow {
    font-weight: 700;
    .percValue {
        font-style: italic;
    }

    .leftAlign {
        text-align: left;
    }
}

th, td {
    border: 2px solid white;
    padding-right: 2px;
    font-size: 7px;
    height: 10px;
}

.domesticTransactionsHeader {
    margin-bottom: -25px;
    padding-left: 7px;
    font-size: 13px;
    color: rgb(60, 132, 197);
    margin-top: -50px;
}

.internationalTransactionsHeader {
    margin-bottom: -25px;
    margin-top: -50px;
    padding-left: 7px;
    font-size: 13px;
    color: rgb(60, 132, 197);
}

.page2TableHeader {
    margin-bottom: -20px;
    padding-left: 3px;
    font-size: 13px;
    color: rgb(60, 132, 197);
    margin-top: -30px;
}

.page2TableHeaderChannel {
    margin-bottom: -20px;
    padding-left: 3px;
    font-size: 13px;
    color: rgb(60, 132, 197);
    margin-top: -20px;
}

.basicTableHeader {
    margin-bottom: -20px;
    padding-left: 3px;
    color: rgb(60, 132, 197);
    font-size: 13px;
    text-align: left;

    .miniHeader {
        padding-bottom: 9px;
        padding-left: 5px;
    }
}

.wideTableHeader {
    margin-bottom: -20px;
    padding-left: 2px;
    color: rgb(60, 132, 197);
    font-size: 13px;
    margin-top: -50px;
}

.pageFooter {
    page-break-after: always;

}

.pageHeader {
    width: 100%;
    line-height: 8px;
    text-align: right;
    font-size: 8px;
    position: relative;
    padding-top: 5px;
    margin-bottom: -10px;

    .tadText {
        font-style: italic;
    }
}

.pageHeader:before {
    counter-increment: page;
    content: counter(page); 
}


</STYLE>