<div>
  <div class="basicTableHeader">
      <h6>Transacting Countries (24 Months) - Receipts</h6>
  </div>
  <table class="conditionalTable">
      <tr class="headers">
          <th class="emptyRow"></th>
          <th class="emptyRow"></th>
          <th colspan="3" class="topHeader">Transaction Value</th>
          <th colspan="3" class="topHeader">Transaction Volume</th>
          <th colspan="2" class="topHeader">% Value of Int. Receipts</th>
          <th colspan="2" class="topHeader">% Value of CR Turnover</th>
      </tr>
      <tr class="headers">
          <th class="firstColumn">Sending Country</th>
          <th class="firstColumn">Risk Rating</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Year-on-Year</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Year-on-Year</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Current Period</th>
          <th>Prior Period</th>
      </tr>
      {{#each transacting_countries_receipts}}
          {{#if (and (ne @index 0) (mod @index 38 ../mod_offset))}}
              </table>
              <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{../tad_template}} for {{../j_number}} up to {{../last_transaction_date}}
      </div>
  </div>
              <table class="conditionalTable">
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="headers">
                      <th class="emptyRow"></th>
                      <th class="emptyRow"></th>
                      <th colspan="3" class="topHeader">Transaction Value</th>
                      <th colspan="3" class="topHeader">Transaction Volume</th>
                      <th colspan="2" class="topHeader">% Value of Int. Receipts</th>
                      <th colspan="2" class="topHeader">% Value of CR Turnover</th>
                  </tr>
                  <tr class="headers">
                      <th class="firstColumn">Sending Country</th>
                      <th class="firstColumn">Risk Rating</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Year-on-Year</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Year-on-Year</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                  </tr>
          {{/if}}
          <tr class="conditionalTableRow">
              <td class="firstColumn">{{sending_country}}</td>
              {{#if (eq sending_country_risk_rating_current_period 'PHR')}}
                  <td class="phrColumn">{{sending_country_risk_rating_current_period}}</td>
              {{/if}}
              {{#if (eq sending_country_risk_rating_current_period 'HR')}}
                  <td class="hrColumn">{{sending_country_risk_rating_current_period}}</td>
              {{/if}}
              {{#if (eq sending_country_risk_rating_current_period 'M/L')}}
                  <td class="mlColumn">{{sending_country_risk_rating_current_period}}</td>
              {{/if}}
              <td>£{{sending_country_value_current_period}}</td>
              <td>£{{sending_country_value_prior_period}}</td>
              <td class="yearOnYear">{{sending_country_transaction_value_year_on_year}}%</td>
              <td>{{sending_country_transaction_volume_current_period}}</td>
              <td>{{sending_country_transaction_volume_prior_period}}</td>
              <td class="yearOnYear">{{sending_country_transaction_volume_year_on_year}}%</td>
              <td class="yearOnYear">{{sending_country_perc_value_of_int_receipts_current_period}}%</td>
              <td class="yearOnYear">{{sending_country_perc_value_of_int_receipts_prior_period}}%</td>
              <td class="yearOnYear">{{sending_country_perc_value_of_cr_turnover_current_period}}%</td>
              <td class="yearOnYear">{{sending_country_perc_value_of_cr_turnover_prior_period}}%</td>
          </tr>
      {{/each}}
  </table>
  <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{tad_template}} for {{j_number}} up to {{last_transaction_date}}
      </div>
  </div>
  <div class="basicTableHeader">
      <h6>Transacting Countries (24 Months) - Payments</h6>
  </div>
  <table class="conditionalTable">
      <tr class="headers">
          <th class="emptyRow"></th>
          <th class="emptyRow"></th>
          <th colspan="3" class="topHeader">Transaction Value</th>
          <th colspan="3" class="topHeader">Transaction Volume</th>
          <th colspan="2" class="topHeader">% Value of Int. Receipts</th>
          <th colspan="2" class="topHeader">% Value of DR Turnover</th>
      </tr>
      <tr class="headers">
          <th class="firstColumn">Receiving Country</th>
          <th class="firstColumn">Risk Rating</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Year-on-Year</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Year-on-Year</th>
          <th>Current Period</th>
          <th>Prior Period</th>
          <th>Current Period</th>
          <th>Prior Period</th>
      </tr>
      {{#each transacting_countries_payments}}
          {{#if (and (ne @index 0) (mod @index 38 0))}}
              </table>
              <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{../tad_template}} for {{../j_number}} up to {{../last_transaction_date}}
      </div>
  </div>
              <table class="conditionalTable">
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="headers">
                      <th class="emptyRow"></th>
                      <th class="emptyRow"></th>
                      <th colspan="3" class="topHeader">Transaction Value</th>
                      <th colspan="3" class="topHeader">Transaction Volume</th>
                      <th colspan="2" class="topHeader">% Value of Int. Receipts</th>
                      <th colspan="2" class="topHeader">% Value of DR Turnover</th>
                  </tr>
                  <tr class="headers">
                      <th class="firstColumn">Receiving Country</th>
                      <th class="firstColumn">Risk Rating</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Year-on-Year</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Year-on-Year</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                      <th>Current Period</th>
                      <th>Prior Period</th>
                  </tr>
          {{/if}}
          <tr class="conditionalTableRow">
              <td class="firstColumn">{{receiving_country}}</td>
              {{#if (eq receiving_country_risk_rating_current_period 'PHR')}}
                  <td class="phrColumn">{{receiving_country_risk_rating_current_period}}</td>
              {{/if}}
              {{#if (eq receiving_country_risk_rating_current_period 'HR')}}
                  <td class="hrColumn">{{receiving_country_risk_rating_current_period}}</td>
              {{/if}}
              {{#if (eq receiving_country_risk_rating_current_period 'M/L')}}
                  <td class="mlColumn">{{receiving_country_risk_rating_current_period}}</td>
              {{/if}}
              <td>£{{receiving_country_value_current_period}}</td>
              <td>£{{receiving_country_value_prior_period}}</td>
              <td class="yearOnYear">{{receiving_country_transaction_value_year_on_year}}%</td>
              <td>{{receiving_country_transaction_volume_current_period}}</td>
              <td>{{receiving_country_transaction_volume_prior_period}}</td>
              <td class="yearOnYear">{{receiving_country_transaction_volume_year_on_year}}%</td>
              <td class="yearOnYear">{{receiving_country_perc_value_of_int_payments_current_period}}%</td>
              <td class="yearOnYear">{{receiving_country_perc_value_of_int_payments_prior_period}}%</td>
              <td class="yearOnYear">{{receiving_country_perc_value_of_dr_turnover_current_period}}%</td>
              <td class="yearOnYear">{{receiving_country_perc_value_of_dr_turnover_prior_period}}%</td>
          </tr>
      {{/each}}
  </table>
  <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{tad_template}} for {{j_number}} up to {{last_transaction_date}}
      </div>
  </div>
  <div class="basicTableHeader">
      <h6>Transacting Counterparties (12 Months) - Receipts</h6>
  </div>
  <table class="conditionalTable">
      <tr class="headers">
          <th class="firstColumn">Sending Country</th>
          <th class="firstColumn">Sender Name</th>
          <th>No. of Transactions</th>
          <th>Current Period</th>
          <th class="firstColumnPercent">% of Country Receipts</th>
          <th class="firstColumn">Risk Rating</th>
      </tr>
      {{#each transacting_counterparties_receipts}}
          {{#if (and (ne @index 0) (mod @index 38 0))}}
              </table>
              <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{../tad_template}} for {{../j_number}} up to {{../last_transaction_date}}
      </div>
  </div>
              <table class="conditionalTable">
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="headers">
                      <th class="firstColumn">Sending Country</th>
                      <th class="firstColumn">Sender Name</th>
                      <th>No. of Transactions</th>
                      <th>Current Period</th>
                      <th class="firstColumnPercent">% of Country Receipts</th>
                      <th class="firstColumn">Risk Rating</th>
                  </tr>
          {{/if}}
          <tr class="conditionalTableRow">
              <td class="firstColumn">{{transacting_counterparties_sending_country}}</td>
              <td>{{transacting_counterparties_sender_name}}</td>
              <td>{{transacting_counterparties_sender_volume_current_period}}</td>
              <td>£{{transacting_counterparties_sender_value_current_period}}</td>
              <td class="yearOnYear">{{transacting_counterparties_perc_of_sending_country_receipts}}%</td>
              {{#if (eq transacting_counterparties_sending_risk_rating 'PHR')}}
                  <td class="phrColumn">{{transacting_counterparties_sending_risk_rating}}</td>
              {{/if}}
              {{#if (eq transacting_counterparties_sending_risk_rating 'HR')}}
                  <td class="hrColumn">{{transacting_counterparties_sending_risk_rating}}</td>
              {{/if}}
              {{#if (eq transacting_counterparties_sending_risk_rating 'M/L')}}
                  <td class="mlColumn">{{transacting_counterparties_sending_risk_rating}}</td>
              {{/if}}
          </tr>
      {{/each}}
  </table>
  <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{tad_template}} for {{j_number}} up to {{last_transaction_date}}
      </div>
  </div>
  <div class="basicTableHeader">
      <h6>Transacting Counterparties (12 Months) - Payments</h6>
  </div>
  <table class="conditionalTable">
      <tr class="headers">
          <th class="firstColumn">Receiving Country</th>
          <th class="firstColumn">Recipient Name</th>
          <th>No. of Transactions</th>
          <th>Current Period</th>
          <th class="firstColumnPercent">% of Country Payments</th>
          <th class="firstColumn">Risk Rating</th>
      </tr>
      {{#each transacting_counterparties_payments}}
          {{#if (and (ne @index 0) (mod @index 38 0))}}
              </table>
              <div class="pageFooter" />
</div>
<div class="page">
  <div class="pageHeader">
      <div class="tadText">
          {{../tad_template}} for {{../j_number}} up to {{../last_transaction_date}}
      </div>
  </div>
              <table class="conditionalTable">
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="emptyRow">
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td></td>
                  </tr>
                  <tr class="headers">
                      <th class="firstColumn">Receiving Country</th>
                      <th class="firstColumn">Recipient Name</th>
                      <th>No. of Transactions</th>
                      <th>Current Period</th>
                      <th class="firstColumnPercent">% of Country Payments</th>
                      <th class="firstColumn">Risk Rating</th>
                  </tr>
          {{/if}}
          <tr class="conditionalTableRow">
              <td class="firstColumn">{{transacting_counterparties_receiving_country}}</td>
              <td>{{transacting_counterparties_receiver_name}}</td>
              <td>{{transacting_counterparties_receiver_volume_current_period}}</td>
              <td>£{{transacting_counterparties_receiver_value_current_period}}</td>
              <td class="yearOnYear">{{transacting_counterparties_perc_of_receiving_country_receipts}}%</td>
              {{#if (eq transacting_counterparties_receiving_risk_rating 'PHR')}}
                  <td class="phrColumn">{{transacting_counterparties_receiving_risk_rating}}</td>
              {{/if}}
              {{#if (eq transacting_counterparties_receiving_risk_rating 'HR')}}
                  <td class="hrColumn">{{transacting_counterparties_receiving_risk_rating}}</td>
              {{/if}}
              {{#if (eq transacting_counterparties_receiving_risk_rating 'M/L')}}
                  <td class="mlColumn">{{transacting_counterparties_receiving_risk_rating}}</td>
              {{/if}}
          </tr>
      {{/each}}
  </table>
  <div class="pageFooter" />
  </div>
</div>