<tr>
  <td class="value">
    Full Legal Name
  </td>
  <td class="value">
    {{MFT0199.old}}
  </td>
  <td class="value">
    {{MFT0199.oldSource}}
  </td>
  <td class="value">
    {{MFT0199.new}}
  </td>
  <td class="value">
    {{MFT0199.newSource}}
  </td>
  <td class="value centered {{MFT0199.diff}}">
    {{MFT0199.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Trading Name
  </td>
  <td class="value">
    {{MFT0038.old}}
  </td>
  <td class="value">
    {{MFT0038.oldSource}}
  </td>
  <td class="value">
    {{MFT0038.new}}
  </td>
  <td class="value">
    {{MFT0038.newSource}}
  </td>
  <td class="value centered {{MFT0038.diff}}">
    {{MFT0038.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Legal Entity Type
  </td>
  <td class="value">
    {{MFT0046.old}}
  </td>
  <td class="value">
    {{MFT0046.oldSource}}
  </td>
  <td class="value">
    {{MFT0046.new}}
  </td>
  <td class="value">
    {{MFT0046.newSource}}
  </td>
  <td class="value centered {{MFT0046.diff}}">
    {{MFT0046.diff}}
  </td>
</tr>
{{#if (and (ne MFT0046.new "sole_trader") (ne MFT0046.new "unincorporated_association") (ne MFT0046.new
"unincorporated_partnership") (ne MFT0046.new "trust"))}}
<tr>
  <td class="value">
    Customer Registered Address Line 1
  </td>
  <td class="value">
    {{[MFT0037.line_one].old}}
  </td>
  <td class="value">
    {{[MFT0037.line_one].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.line_one].new}}
  </td>
  <td class="value">
    {{[MFT0037.line_one].newSource}}
  </td>
  <td class="value centered {{[MFT0037.line_one].diff}}">
    {{[MFT0037.line_one].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Registered Address Line 2
  </td>
  <td class="value">
    {{[MFT0037.line_two].old}}
  </td>
  <td class="value">
    {{[MFT0037.line_two].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.line_two].new}}
  </td>
  <td class="value">
    {{[MFT0037.line_two].newSource}}
  </td>
  <td class="value centered {{[MFT0037.line_two].diff}}">
    {{[MFT0037.line_two].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Registered Address Line 3
  </td>
  <td class="value">
    {{[MFT0037.line_three].old}}
  </td>
  <td class="value">
    {{[MFT0037.line_three].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.line_three].new}}
  </td>
  <td class="value">
    {{[MFT0037.line_three].newSource}}
  </td>
  <td class="value centered {{[MFT0037.line_three].diff}}">
    {{[MFT0037.line_three].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Registered Address Line 4
  </td>
  <td class="value">
    {{[MFT0037.line_four].old}}
  </td>
  <td class="value">
    {{[MFT0037.line_four].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.line_four].new}}
  </td>
  <td class="value">
    {{[MFT0037.line_four].newSource}}
  </td>
  <td class="value centered {{[MFT0037.line_four].diff}}">
    {{[MFT0037.line_four].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Registered Address Country
  </td>
  <td class="value">
    {{[MFT0037.ctry].old}}
  </td>
  <td class="value">
    {{[MFT0037.ctry].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.ctry].new}}
  </td>
  <td class="value">
    {{[MFT0037.ctry].newSource}}
  </td>
  <td class="value centered {{[MFT0037.ctry].diff}}">
    {{[MFT0037.ctry].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Registered Address Post code
  </td>
  <td class="value">
    {{[MFT0037.post_code].old}}
  </td>
  <td class="value">
    {{[MFT0037.post_code].oldSource}}
  </td>
  <td class="value">
    {{[MFT0037.post_code].new}}
  </td>
  <td class="value">
    {{[MFT0037.post_code].newSource}}
  </td>
  <td class="value centered {{[MFT0037.post_code].diff}}">
    {{[MFT0037.post_code].diff}}
  </td>
</tr>
{{/if}}
<tr>
  <td class="value">
    Does your trading address match your registered address?
  </td>
  <td class="value">
    {{MFT1451.old}}
  </td>
  <td class="value">
    {{MFT1451.oldSource}}
  </td>
  <td class="value">
    {{MFT1451.new}}
  </td>
  <td class="value">
    {{MFT1451.newSource}}
  </td>
  <td class="value centered {{MFT1451.diff}}">
    {{MFT1451.diff}}
  </td>
</tr>
{{#if (or (eq MFT0046.new "sole_trader") (eq MFT1451.new "no"))}}
<tr>
  <td class="value">
    Customer Trading Address Line 1
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.line_one].old}}
  </td>
  <td class="value">
    {{[MFT3264.line_one].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.line_one].new}}
  </td>
  <td class="value">
    {{[MFT3264.line_one].newSource}}
  </td>
  <td class="value centered {{[MFT3264.line_one].diff}}">
    {{[MFT3264.line_one].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.line_one].old}}
  </td>
  <td class="value">
    {{[MFT0030.line_one].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.line_one].new}}
  </td>
  <td class="value">
    {{[MFT0030.line_one].newSource}}
  </td>
  <td class="value centered {{[MFT0030.line_one].diff}}">
    {{[MFT0030.line_one].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
<tr>
  <td class="value">
    Customer Trading Address Line 2
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.line_two].old}}
  </td>
  <td class="value">
    {{[MFT3264.line_two].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.line_two].new}}
  </td>
  <td class="value">
    {{[MFT3264.line_two].newSource}}
  </td>
  <td class="value centered {{[MFT3264.line_two].diff}}">
    {{[MFT3264.line_two].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.line_two].old}}
  </td>
  <td class="value">
    {{[MFT0030.line_two].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.line_two].new}}
  </td>
  <td class="value">
    {{[MFT0030.line_two].newSource}}
  </td>
  <td class="value centered {{[MFT0030.line_two].diff}}">
    {{[MFT0030.line_two].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
<tr>
  <td class="value">
    Customer Trading Address Line 3
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.line_three].old}}
  </td>
  <td class="value">
    {{[MFT3264.line_three].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.line_three].new}}
  </td>
  <td class="value">
    {{[MFT3264.line_three].newSource}}
  </td>
  <td class="value centered {{[MFT3264.line_three].diff}}">
    {{[MFT3264.line_three].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.line_three].old}}
  </td>
  <td class="value">
    {{[MFT0030.line_three].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.line_three].new}}
  </td>
  <td class="value">
    {{[MFT0030.line_three].newSource}}
  </td>
  <td class="value centered {{[MFT0030.line_three].diff}}">
    {{[MFT0030.line_three].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
<tr>
  <td class="value">
    Customer Trading Address Line 4
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.line_four].old}}
  </td>
  <td class="value">
    {{[MFT3264.line_four].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.line_four].new}}
  </td>
  <td class="value">
    {{[MFT3264.line_four].newSource}}
  </td>
  <td class="value centered {{[MFT3264.line_four].diff}}">
    {{[MFT3264.line_four].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.line_four].old}}
  </td>
  <td class="value">
    {{[MFT0030.line_four].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.line_four].new}}
  </td>
  <td class="value">
    {{[MFT0030.line_four].newSource}}
  </td>
  <td class="value centered {{[MFT0030.line_four].diff}}">
    {{[MFT0030.line_four].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
<tr>
  <td class="value">
    Customer Trading Address Country
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.ctry].old}}
  </td>
  <td class="value">
    {{[MFT3264.ctry].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.ctry].new}}
  </td>
  <td class="value">
    {{[MFT3264.ctry].newSource}}
  </td>
  <td class="value centered {{[MFT3264.ctry].diff}}">
    {{[MFT3264.ctry].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.ctry].old}}
  </td>
  <td class="value">
    {{[MFT0030.ctry].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.ctry].new}}
  </td>
  <td class="value">
    {{[MFT0030.ctry].newSource}}
  </td>
  <td class="value centered {{[MFT0030.ctry].diff}}">
    {{[MFT0030.ctry].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
<tr>
  <td class="value">
    Customer Trading Address Post code
  </td>
  {{#if (eq MFT0046.new "sole_trader")}}
  <td class="value">
    {{[MFT3264.post_code].old}}
  </td>
  <td class="value">
    {{[MFT3264.post_code].oldSource}}
  </td>
  <td class="value">
    {{[MFT3264.post_code].new}}
  </td>
  <td class="value">
    {{[MFT3264.post_code].newSource}}
  </td>
  <td class="value centered {{[MFT3264.post_code].diff}}">
    {{[MFT3264.post_code].diff}}
  </td>
  {{else}}
  {{#if (eq MFT1451.new "no")}}
  <td class="value">
    {{[MFT0030.post_code].old}}
  </td>
  <td class="value">
    {{[MFT0030.post_code].oldSource}}
  </td>
  <td class="value">
    {{[MFT0030.post_code].new}}
  </td>
  <td class="value">
    {{[MFT0030.post_code].newSource}}
  </td>
  <td class="value centered {{[MFT0030.post_code].diff}}">
    {{[MFT0030.post_code].diff}}
  </td>
  {{/if}}
  {{/if}}
</tr>
{{/if}}
{{#if (or (eq MFT0046.new "unincorporated_association") (eq MFT0046.new "unincorporated_partnership"))}}
<tr>
  <td class="value">
    Customer Operating Address Line 1
  </td>
  <td class="value">
    {{[MFT6769.line_one].old}}
  </td>
  <td class="value">
    {{[MFT6769.line_one].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.line_one].new}}
  </td>
  <td class="value">
    {{[MFT6769.line_one].newSource}}
  </td>
  <td class="value centered {{[MFT6769.line_one].diff}}">
    {{[MFT6769.line_one].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Operating Address Line 2
  </td>
  <td class="value">
    {{[MFT6769.line_two].old}}
  </td>
  <td class="value">
    {{[MFT6769.line_two].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.line_two].new}}
  </td>
  <td class="value">
    {{[MFT6769.line_two].newSource}}
  </td>
  <td class="value centered {{[MFT6769.line_two].diff}}">
    {{[MFT6769.line_two].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Operating Address Line 3
  </td>
  <td class="value">
    {{[MFT6769.line_three].old}}
  </td>
  <td class="value">
    {{[MFT6769.line_three].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.line_three].new}}
  </td>
  <td class="value">
    {{[MFT6769.line_three].newSource}}
  </td>
  <td class="value centered {{[MFT6769.line_three].diff}}">
    {{[MFT6769.line_three].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Operating Address Line 4
  </td>
  <td class="value">
    {{[MFT6769.line_four].old}}
  </td>
  <td class="value">
    {{[MFT6769.line_four].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.line_four].new}}
  </td>
  <td class="value">
    {{[MFT6769.line_four].newSource}}
  </td>
  <td class="value centered {{[MFT6769.line_four].diff}}">
    {{[MFT6769.line_four].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Operating Address Country
  </td>
  <td class="value">
    {{[MFT6769.ctry].old}}
  </td>
  <td class="value">
    {{[MFT6769.ctry].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.ctry].new}}
  </td>
  <td class="value">
    {{[MFT6769.ctry].newSource}}
  </td>
  <td class="value centered {{[MFT6769.ctry].diff}}">
    {{[MFT6769.ctry].diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Customer Operating Address Post Code
  </td>
  <td class="value">
    {{[MFT6769.post_code].old}}
  </td>
  <td class="value">
    {{[MFT6769.post_code].oldSource}}
  </td>
  <td class="value">
    {{[MFT6769.post_code].new}}
  </td>
  <td class="value">
    {{[MFT6769.post_code].newSource}}
  </td>
  <td class="value centered {{[MFT6769.post_code].diff}}">
    {{[MFT6769.post_code].diff}}
  </td>
</tr>
{{/if}}
{{#if (or (eq MFT0046.new "uk_company_limited_by_guarantee") (eq MFT0046.old "uk_company_limited_by_guarantee"))}}
<tr>
  <td class="value">
    Limited By Guarantee Attestation One
  </td>
  <td class="value">
    {{MFT4228.old}}
  </td>
  <td class="value">
    {{MFT4228.oldSource}}
  </td>
  <td class="value">
    {{MFT4228.new}}
  </td>
  <td class="value">
    {{MFT4228.newSource}}
  </td>
  <td class="value centered {{MFT4228.diff}}">
    {{MFT4228.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Limited By Guarantee Attestation Two
  </td>
  <td class="value">
    {{MFT4229.old}}
  </td>
  <td class="value">
    {{MFT4229.oldSource}}
  </td>
  <td class="value">
    {{MFT4229.new}}
  </td>
  <td class="value">
    {{MFT4229.newSource}}
  </td>
  <td class="value centered {{MFT4229.diff}}">
    {{MFT4229.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Limited By Guarantee Attestation Three
  </td>
  <td class="value">
    {{MFT4230.old}}
  </td>
  <td class="value">
    {{MFT4230.oldSource}}
  </td>
  <td class="value">
    {{MFT4230.new}}
  </td>
  <td class="value">
    {{MFT4230.newSource}}
  </td>
  <td class="value centered {{MFT4230.diff}}">
    {{MFT4230.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Limited By Guarantee Attestation Four
  </td>
  <td class="value">
    {{MFT4231.old}}
  </td>
  <td class="value">
    {{MFT4231.oldSource}}
  </td>
  <td class="value">
    {{MFT4231.new}}
  </td>
  <td class="value">
    {{MFT4231.newSource}}
  </td>
  <td class="value centered {{MFT4231.diff}}">
    {{MFT4231.diff}}
  </td>
</tr>
<tr>
  <td class="value">
    Portal Submitter
  </td>
  <td class="value">
    {{MFT4232.old}}
  </td>
  <td class="value">
    {{MFT4232.oldSource}}
  </td>
  <td class="value">
    {{MFT4232.new}}
  </td>
  <td class="value">
    {{MFT4232.newSource}}
  </td>
  <td class="value centered {{MFT4232.diff}}">
    {{MFT4232.diff}}
  </td>
</tr>
{{/if}}
