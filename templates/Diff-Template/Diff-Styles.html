<style type="text/css">
  * {
    font-family: sans-serif;
    line-height: 1.5;
  }

  .centered {
    text-align: center;
  }

  header {
    width: 100%;
    position: relative;
    padding-top: 5px;
    color: rgb(60, 132, 197);
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 40px;

    .title {
      float: left;
      width: 50%;
      text-align: left;
    }

    .id {
      float: right;
      width: 50%;
      text-align: right;
    }
  }

  table {
    font-size: 12px;
    min-width: 100%;
    background-color: #eeeeee;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
    margin-bottom: 50px;

    tr {
      page-break-inside: avoid;

      td {
        border: 2px solid white;
        padding: 5px;

        &.title {
          background-color: rgb(60, 132, 197);
          color: white;
          font-size: 16px;
          font-weight: bold;
        }

        &.header {
          background-color: rgb(60, 132, 197);
          font-weight: bold;
          color: white;

          &.source {
            width: 10%;
          }
        }

        &.value {
          &.Yes {
            color: red;
            font-weight: bold;
          }
        }
      }
    }
  }

  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    margin-top: 50px;
    font-size: 14px;
    font-weight: bold;
    border-top: 1px solid black;
  }
</style>
