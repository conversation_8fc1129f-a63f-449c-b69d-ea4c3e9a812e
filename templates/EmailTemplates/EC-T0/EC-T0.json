{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "taskId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "salutationName": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "securityPostcode": {"type": "string", "minLength": 1}, "username": {"type": "string", "minLength": 1}}, "required": ["contentHost", "taskId", "emailType", "salutationName", "fullLegalEntityName", "securityPostcode", "username"]}