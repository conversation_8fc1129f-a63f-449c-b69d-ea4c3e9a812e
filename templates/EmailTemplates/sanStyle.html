<style type="text/css">
    /* Global and Main Asset Styling Relevant To All Templates*/
    body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100% !important;
        -ms-text-size-adjust: 100% !important;
        -webkit-font-smoothing: antialiased !important;
    }

    img {
        border: 0 !important;
        outline: none !important;
        pointer-events: none;
    }

    p {
        Margin: 0px !important;
        Padding: 0px !important;
    }

    table {
        border-collapse: collapse;
        mso-table-lspace: 0px;
        mso-table-rspace: 0px;
    }

    td,
    a,
    span {
        border-collapse: collapse;
        mso-line-height-rule: exactly;
    }

    .ExternalClass * {
        line-height: 100%;
    }

    .st_defaultlink a {
        color: inherit !important;
        text-decoration: none !important;
    }

    /*carousel classes*/
    .container {
        width: 540px;
        padding: 0 !important;
    }

    .mobile {
        width: 540px;
        display: block;
        padding: 0 !important;
        text-align: center !important;
    }

    .st_mobileOnly {
        display: none !important;
    }

    /* Responsive Styling */
    @media only screen and (min-width:481px) and (max-width:667px) {
        .st_stack {
            display: block;
            width: 100% !important;
        }

        .st_mobileCenter {
            max-width: none;
            text-align: center;
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .st_main_table {
            width: 100% !important;
        }

        .st_wrapper {
            width: 100% !important;
        }

        .st_w95 {
            width: 95% !important;
        }

        .st_hide {
            display: none !important;
        }

        .st_hauto {
            height: auto !important;
        }

        .st_h25p {
            width: 25% !important;
        }

        .st_full_img {
            width: 100% !important;
            height: auto !important;
        }

        .st_center {
            text-align: center !important;
        }

        .st_left {
            text-align: left !important;
        }

        .st_right {
            text-align: right !important;
        }

        .st_side10 {
            width: 10px !important;
        }

        .st_aside15 {
            padding: 0px 15px !important;
        }

        .st_h20 {
            height: 20px !important;
        }

        .st_ptop {
            padding-top: 20px !important;
        }

        .st_ptop10 {
            padding-top: 10px !important;
        }

        .st_pbottom {
            padding-bottom: 20px !important;
        }

        .st_pright {
            padding-right: 43px !important;
        }

        .st_hide_desktop {
            display: table !important;
            float: none !important;
            width: 100% !important;
            overflow: visible !important;
            height: auto !important;
        }

        .st_ptop30 {
            padding-top: 30px !important;
        }

        .st_pbottom30 {
            padding-bottom: 30px !important;
        }

        .st_wid147 {
            width: 147px !important;
            height: auto !important;
            max-width: none !important;
        }

        .st_wid115 {
            width: 115px !important;
            height: auto !important;
            max-width: none !important;
        }

        .st_wid100 {
            width: 100px !important;
            height: auto !important;
            max-width: none !important;
        }

        .st_w65p {
            width: 65% !important;
            height: auto !important;
            max-width: none !important;
        }

        .st_border {
            border-bottom: 1px solid #959595 !important;
        }

        .st_h57 {
            height: 57px !important;
        }

        *[class=st_subtext] {
            font-size: 16px !important;
            mso-ansi-font-size: 12pt !important;
            font-family: 'Open Sans', Arial, sans-serif;
        }

        table[class*=st_hide],
        tr[class*=st_hide],
        td[class*=st_hide],
        img[class*=st_hide],
        span[class*="st_hide"],
        div[class*=st_hide] {
            display: none !important;
        }

        .st_mobileOnly {
            display: block !important;
        }

        /**[class=tableForegroundText] { font-size: 28px; mso-ansi-font-size:21pt; line-height:36px !important; font-family:'Open Sans', Arial, sans-serif !important; }*/
        *[class=tableForegroundText] {
            font-size: 26px;
            mso-ansi-font-size: 19.5pt;
            line-height: 30px !important;
            font-family: 'Open Sans', Arial, sans-serif !important;
        }

        .st_img {
            width: 100% !important;
            height: auto !important;
        }

        .st_aside10 {
            padding: 0px 10px !important;
        }

        .st_side15 {
            width: 15px !important;
        }

        .st_bgnone {
            background-image: none !important;
            height: auto !important;
        }

        .st_graybor {
            border: 10px solid #bebcbc !important;
        }

        .st_h10 {
            height: 10px !important;
            line-height: 0px !important;
            font-size: 0px !important;
        }

        .st_img85 {
            width: 100px !important;
            height: auto !important;
        }

        .st_pad30 {
            padding-top: 0px !important;
        }

        .st_h1 {
            height: 10px !important;
            font-size: 0px !important;
            line-height: 0px !important;
        }

        .st_h230 {
            height: 230px !important;
        }

        .st_h55 {
            height: 50px !important;
        }

        .st_w50 {
            width: 50% !important;
        }

        .st_img2 {
            width: 100% !important;
            height: auto !important;
        }

        .st_img3 {
            width: 80% !important;
            height: auto !important;
        }

        .st_h40 {
            height: 40px !important;
        }

        .st_h20p {
            height: 20% !important;
        }
    }

    @media only screen and (min-width:480px) {
        .mj-column-per-100 {
            width: 100% !important;
        }
    }

    @media only screen and (min-width:375px) and (max-width:480px) {
        .st_stack {
            display: block;
            width: 100% !important;
        }

        .st_mobilePad {
            width: 10%;
        }

        .st_aside {
            padding-left: 37px;
            padding-right: 45px;
        }

        .st_mobileCenter {
            max-width: none;
            text-align: center;
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .st_main_table {
            width: 375px !important;
        }

        .st_wrapper {
            width: 100% !important;
        }

        .st_mobileBlock {
            display: block !important;
        }

        .st_w95 {
            width: 95% !important;
        }

        .st_hide {
            display: none !important;
        }

        .st_img {
            width: 100% !important;
            height: auto !important;
        }

        .st_center {
            text-align: center !important;
        }

        .st_left {
            text-align: left !important;
        }

        .st_right {
            text-align: right !important;
        }

        .st_side10 {
            width: 10px !important;
        }

        .st_aside10 {
            padding: 0px 10px !important;
        }

        .st_side15 {
            width: 15px !important;
        }

        .st_aside15 {
            padding: 0px 15px !important;
        }

        .st_h20 {
            height: 20px !important;
        }

        .st_hauto {
            height: auto !important;
        }

        .st_h25p {
            width: 25% !important;
        }

        .st_padNone {
            padding: 0px !important;
        }

        .st_ptop {
            padding-top: 20px !important;
        }

        .st_ptop10 {
            padding-top: 10px !important;
        }

        .st_pbottom {
            padding-bottom: 20px !important;
        }

        .st_hide_desktop {
            display: table !important;
            float: none !important;
            width: 100% !important;
            overflow: visible !important;
            height: auto !important;
        }

        u+.st_body .st_full_wrap {
            width: 100% !important;
            width: 100vw !important;
        }

        .st_img1 {
            width: 70% !important;
            height: auto !important;
        }

        .st_pad0 {
            padding-top: 0px !important;
        }

        .st_w50 {
            width: 50% !important;
        }

        .st_w83 {
            width: 83% !important;
        }

        .st_bgnone {
            background-image: none !important;
            height: auto !important;
        }

        .st_graybor {
            border: 10px solid #bebcbc !important;
        }

        .st_h10 {
            height: 10px !important;
            line-height: 0px !important;
            font-size: 0px !important;
        }

        .st_img85 {
            width: 100px !important;
            height: auto !important;
        }

        .st_pad30 {
            padding-top: 0px !important;
        }

        .st_h1 {
            height: 0px !important;
        }

        .st_buttonfont {
            font-size: 18px !important;
        }

        .st_h55 {
            height: 50px !important;
        }

        .st_img2 {
            width: 100% !important;
            height: auto !important;
        }

        .st_img3 {
            width: 80% !important;
            height: auto !important;
        }

        .st_h40 {
            height: 40px !important;
        }

        .st_w65p {
            width: 65% !important;
            height: auto !important;
            max-width: none !important;
        }

        *[class=st_subtext] {
            font-size: 16px !important;
            mso-ansi-font-size: 12pt !important;
            font-family: 'Open Sans', Arial, sans-serif;
        }

        table[class*=st_hide],
        tr[class*=st_hide],
        td[class*=st_hide],
        img[class*=st_hide],
        span[class*="st_hide"],
        div[class*=st_hide] {
            display: none !important;
        }

        .st_mobileOnly {
            display: block !important;
        }

        /**[class=tableForegroundText] { font-size: 28px; mso-ansi-font-size:21pt; line-height:36px !important; font-family:'Open Sans', Arial, sans-serif !important; }*/
        *[class=tableForegroundText] {
            font-size: 26px;
            mso-ansi-font-size: 19.5pt;
            line-height: 30px !important;
            font-family: 'Open Sans', Arial, sans-serif !important;
        }

        .st_h20p {
            height: 20% !important;
        }

        .st_h230 {
            height: 145px !important;
        }
    }

    @media screen and (max-width: 375px) {
        .st_stack {
            display: block;
            width: 100% !important;
        }

        .st_mobilePad {
            width: 10%;
        }

        .st_aside {
            padding-left: 37px;
            padding-right: 45px;
        }

        .st_mobileCenter {
            max-width: none;
            text-align: center;
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .st_main_table {
            width: 320px !important;
        }

        .st_wrapper {
            width: 100% !important;
        }

        .st_w95 {
            width: 95% !important;
        }

        .st_hide {
            display: none !important;
        }

        .st_mobileBlock {
            display: block !important;
        }

        .st_img {
            width: 100% !important;
            height: auto !important;
        }

        .st_img3 {
            width: 85% !important;
            height: auto !important;
        }

        .st_center {
            text-align: center !important;
        }

        .st_left {
            text-align: left !important;
        }

        .st_right {
            text-align: right !important;
        }

        .st_side10 {
            width: 10px !important;
        }

        .st_aside10 {
            padding: 0px 10px !important;
        }

        .st_side15 {
            width: 15px !important;
        }

        .st_aside15 {
            padding: 0px 15px !important;
        }

        .st_h20 {
            height: 20px !important;
        }

        .st_hauto {
            height: auto !important;
        }

        .st_h25p {
            width: 25% !important;
        }

        .st_padNone {
            padding: 0px !important;
        }

        .st_ptop {
            padding-top: 20px !important;
        }

        .st_pbottom {
            padding-bottom: 20px !important;
        }

        .st_hide_desktop {
            display: table !important;
            float: none !important;
            width: 100% !important;
            overflow: visible !important;
            height: auto !important;
        }

        u+.st_body .st_full_wrap {
            width: 100% !important;
            width: 100vw !important;
        }

        .st_img1 {
            width: 70% !important;
            height: auto !important;
        }

        .st_pad0 {
            padding-top: 0px !important;
        }

        .st_w50 {
            width: 50% !important;
        }

        .st_bgnone {
            background-image: none !important;
            height: auto !important;
        }

        .st_h35 {
            height: 35px !important;
        }

        .st_titler {
            font-size: 18px !important;
            line-height: 22px !important;
        }

        .st_graybor {
            border: 10px solid #bebcbc !important;
        }

        .st_h10 {
            height: 10px !important;
            line-height: 0px !important;
            font-size: 0px !important;
        }

        .st_img85 {
            width: 85px !important;
            height: auto !important;
        }

        .st_pad30 {
            padding-top: 15px !important;
        }

        .st_h1 {
            height: 0px !important;
        }

        .st_h55 {
            height: 40px !important;
        }

        .st_img2 {
            width: 100% !important;
            height: auto !important;
            max-width: none !important;
        }

        .st_h40 {
            height: 40px !important;
        }

        .st_w65p {
            width: 65% !important;
            height: auto !important;
            max-width: none !important;
        }

        *[class=st_subtext] {
            font-size: 16px !important;
            mso-ansi-font-size: 12pt !important;
            font-family: 'Open Sans', Arial, sans-serif;
        }

        table[class*=st_hide],
        tr[class*=st_hide],
        td[class*=st_hide],
        img[class*=st_hide],
        span[class*="st_hide"],
        div[class*=st_hide] {
            display: none !important;
        }

        .st_mobileOnly {
            display: block !important;
        }

        /**[class=tableForegroundText] { font-size: 28px; mso-ansi-font-size:21pt; line-height:36px !important; font-family:'Open Sans', Arial, sans-serif !important; }*/
        *[class=tableForegroundText] {
            font-size: 26px;
            mso-ansi-font-size: 19.5pt;
            line-height: 30px !important;
            font-family: 'Open Sans', Arial, sans-serif !important;
        }

        .st_h20p {
            height: 20% !important;
        }

        .st_h230 {
            height: 105px !important;
        }
    }

    @media screen and (max-width: 320px) {
        .st_aside {
            padding-left: 37px;
            padding-right: 42px;
        }
    }

    @media screen and (max-width: 280px) {
        .st_aside {
            padding-left: 37px;
            padding-right: 42px;
        }
    }
</style>
<style>
    .shsp-wireframe-layout-preheader {
        display: none;
    }
</style>
