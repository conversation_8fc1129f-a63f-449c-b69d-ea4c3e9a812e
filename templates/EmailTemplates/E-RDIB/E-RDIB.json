{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "caseId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "rmName": {"type": "string", "minLength": 1}, "restrictionDeadline": {"type": "string", "minLength": 1}}, "required": ["contentHost", "caseId", "emailType", "fullLegalEntityName", "rmName", "restrictionDeadline"]}