{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "taskId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "rmName": {"type": "string", "minLength": 1}, "applicationId": {"type": "string", "minLength": 1}}, "required": ["contentHost", "taskId", "emailType", "fullLegalEntityName", "rmName", "applicationId"]}