{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "caseId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "salutationName": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "securityPostcode": {"type": "string", "minLength": 1}, "restrictionDeadline": {"type": "string", "minLength": 1}}, "required": ["contentHost", "caseId", "emailType", "salutationName", "fullLegalEntityName", "securityPostcode", "restrictionDeadline"]}