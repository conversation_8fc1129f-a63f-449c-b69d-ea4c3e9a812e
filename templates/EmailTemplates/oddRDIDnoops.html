{{#if (eq noOpsStatus "Off")}}
<td valign="top" align="left" style="padding-right:16px;padding-left: 16px;"
        class="st_aside15">
        <font
            style="font-size:14px;font-family: 'Open Sans', Arial, sans-serif;color:#000000; line-height: 17px;">
            <b>Customer Verification - please support in resubmitting some documentation for your client {{fullLegalEntityName}} by {{restrictionDeadline}}</b>
        </font>
</td>
{{/if}}

{{#if (eq noOpsStatus "On")}}
<td valign="top" align="left" style="padding-right:16px;padding-left: 16px;"
        class="st_aside15">
        <font
            style="font-size:14px;font-family: 'Open Sans', Arial, sans-serif;color:#000000; line-height: 17px;">
            <b>Customer Verification - please support in resubmitting some documentation for your client {{fullLegalEntityName}}</b>
        </font>
</td>
{{/if}}


{{#if (eq noOpsStatus "Instruct to Apply")}}
<td valign="top" align="left" style="padding-right:16px;padding-left: 16px;"
        class="st_aside15">
        <font
            style="font-size:14px;font-family: 'Open Sans', Arial, sans-serif;color:#000000; line-height: 17px;">
            <b>Customer Verification - please support in resubmitting some documentation for your client {{fullLegalEntityName}}</b>
        </font>
</td>
{{/if}}


{{#if (eq noOpsStatus "Instruct to Remove")}}
<td valign="top" align="left" style="padding-right:16px;padding-left: 16px;"
        class="st_aside15">
        <font
            style="font-size:14px;font-family: 'Open Sans', Arial, sans-serif;color:#000000; line-height: 17px;">
            <b>Customer Verification - please support in resubmitting some documentation for your client {{fullLegalEntityName}}</b>
        </font>
</td>
{{/if}}
