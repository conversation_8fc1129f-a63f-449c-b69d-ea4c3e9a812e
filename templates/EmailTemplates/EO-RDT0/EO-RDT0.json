{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "taskId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "rmName": {"type": "string", "minLength": 1}, "restrictionDeadline": {"type": "string", "minLength": 1}, "reviewType": {"type": "string", "minLength": 1}}, "required": ["contentHost", "taskId", "emailType", "fullLegalEntityName", "rmName", "restrictionDeadline", "reviewType"]}