{"type": "object", "required": ["contentHost", "taskId", "emailType", "salutationName", "fullLegalEntityName", "securityPostcode", "username", "noOpsStatus", "restrictionDeadline", "documents"], "properties": {"contentHost": {"type": "string", "minLength": 1}, "taskId": {"type": "string", "minLength": 1}, "emailType": {"type": "string", "minLength": 1}, "salutationName": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "securityPostcode": {"type": "string", "minLength": 1}, "restrictionDeadline": {"type": "string", "minLength": 1}, "noOpsStatus": {"type": "string", "minLength": 1}, "username": {"type": "string", "minLength": 1}, "documents": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["reason", "document", "customer"], "properties": {"reason": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["reason"], "properties": {"reason": {"type": "string", "minLength": 1}}}}, "customer": {"type": "string", "minLength": 1}, "document": {"type": "string", "minLength": 1}}}}}}