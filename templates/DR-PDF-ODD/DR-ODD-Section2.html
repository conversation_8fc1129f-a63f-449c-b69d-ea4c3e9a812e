<div class="section2">
  <h3 class="mainTitle">
    Section 2: Review Summary
  </h3>

  <table class="firstTable">
    <tr>
      <td class="tableHeadRow" colspan="4">
        Relationship Manager Details
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        Franchise
      </td>
      <td class="valueColumn">
       {{MFT0083}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        RM Region
      </td>
      <td class="valueColumn">
        {{MFT2519}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        RM Name
      </td>
      <td class="valueColumn">
        {{MFT0088}}
      </td>
    </tr>
    <tr>
      <td class="tableHeadRow" colspan="4">
        Principle Contact Details
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        PC Name
      </td>
      <td class="valueColumn">
       {{MFT3627}} {{MFT3626}} {{MFT3629}} {{MFT3628}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        PC Phone Number
      </td>
      <td class="valueColumn">
        {{MFT3643}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        PC Email Address
      </td>
      <td class="valueColumn">
        {{MFT3642}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        PC Residential Address
      </td>
      <td class="valueColumn">
        {{MFT7986}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        PC Date of Birth
      </td>
      <td class="valueColumn">
        {{MFT7973}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        Correspondence Address
      </td>
      <td class="valueColumn">
        {{MFT0674.lineOne}}
        {{MFT0674.lineTwo}}
        {{MFT0674.lineThree}}
        {{MFT0674.lineFour}}
        {{MFT0674.postcode}}
        {{MFT0674.country}}
      </td>
    </tr>
    {{#if (ne MFT7728 "onboarding")}}
      <tr>
        <td class="tableHeadRow" colspan="4">
          Event Summary
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Trigger_IDs
        </td>
        <td class="valueColumn">
          {{MFT7735}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Event Type
        </td>
        <td class="valueColumn">
          {{MFT7737}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Resource ID
        </td>
        <td class="valueColumn">
          {{MFT7783}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Event Rationale
        </td>
        <td class="valueColumn">
          {{MFT7745}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          ARIs Applied
        </td>
        <td class="valueColumn">
          {{MFT7784}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Event Origination
        </td>
        <td class="valueColumn">
          {{MFT7782}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Event Source
        </td>
        <td class="valueColumn">
          {{MFT7739}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Date Raised to KPMG
        </td>
        <td class="valueColumn">
          {{MFT7743}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Event Detected on SAN Systems Timestamp
        </td>
        <td class="valueColumn">
          {{MFT7742}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          SAN Event ID
        </td>
        <td class="valueColumn">
          {{MFT7736}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Change Detected Date
        </td>
        <td class="valueColumn">
          {{MFT7741}}
        </td>
      </tr>
    {{/if}}
    {{#if (eq MFT7728 "onboarding")}}
      <tr>
        <td class="tableHeadRow" colspan="4">
          Onboarding Details
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Form ID
        </td>
        <td class="valueColumn">
          {{MFTTBC1}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          S-Record
        </td>
        <td class="valueColumn">
          {{MFTNEW3}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Onboarding Type
        </td>
        <td class="valueColumn">
          {{MFTNEW2}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          Form Submission Date
        </td>
        <td class="valueColumn">
          {{MFTNEW1}}
        </td>
      </tr>
      <tr>
        <td class="titleColumn">
          CDD Request Date
        </td>
        <td class="valueColumn">
          {{MFTNEW4}}
        </td>
      </tr>
    {{/if}}
    <tr>
      <td class="tableHeadRow" colspan="4">
        Outreach Summary
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        Customer Outreach Triggered Date
      </td>
      <td class="valueColumn">
        {{#if MFT2353}}
          {{MFT2353}}
        {{else}}
          This customer did not require Customer Outreach as part of this review.
        {{/if}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        Customer Submitted Date
      </td>
      <td class="valueColumn">
        {{#if MFT3584}}
          {{MFT3584}}
        {{else}}
          This customer did not require Customer Outreach as part of this review.
        {{/if}}
      </td>
    </tr>
    <tr>
      <td class="titleColumn">
        Portal Resubmission Date
      </td>
      <td class="valueColumn">
        {{#if MFT5294}}
          {{MFT5294}} 
        {{else}}
          A resubmit was not required as part of this review.
        {{/if}}
      </td>
    </tr>
  </table>
</div>