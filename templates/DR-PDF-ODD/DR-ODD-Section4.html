<div class="section4">
  <h3 class="mainTitle">Section 4: KYC Risk Indicators</h3>
  <h2 class="subTitle">Santander Original CRA (Risk Rating) Summary</h2>

  <table>
    <tr>
      <td class="titleColumn">Original Risk Rating</td>
      <td class="valueColumn">{{MFT0023}}</td>
    </tr>
  </table>

  <h2 class="subTitle">Final CRA (Risk Rating) Summary</h2>
  <table>
    <tr>
      <td class="titleColumn">Overall</td>
      <td class="titleColumn">Overall Risk Rating</td>
      <td class="titleColumn">Overall Risk Score</td>
    </tr>
    <tr>
      <td class="titleRow">
        KPMG CRA Risk Rating
      </td>
      <td class="craCell">{{MFT5101}}</td>
      <td class="craCell" >{{MFT5100}}</td>
    </tr>
  </table>
  <table>
    <tr>
      <td class="titleColumn">Summary of CRA</td>
      <td class="titleColumn">CRA Input</td>
      <td class="titleColumn">Risk Rating</td>
      <td class="titleColumn">Risk Score</td>
    </tr>   
    <tr>
      <td class="titleRow">Product</td>
      <td class="craCell">{{MFT7574}}</td>
      <td class="riskCell">{{MFT5099}}</td>
      <td>{{MFT7592}}</td>
    </tr>
    <tr>
      <td class="titleRow">SIC Code</td>
      <td class="craCell">
        {{#each MFT7562}}
        {{#if @last}}{{this}}
        {{else}}{{this}}, 
        {{/if}}
        {{/each}}
      </td>
      <td class="riskCell">{{MFT7593}}</td>
      <td>{{MFT7594}}</td>
    </tr>
    <tr>
      <td class="titleRow">CRA supplementary question answered?</td>
      <td class="craCell">{{MFT7567}}</td>
      <td class="riskCell">N/A</td>
      <td>N/A</td>
    </tr>
    <tr>
      <td class="titleRow">Entity Type</td>
      <td class="craCell">{{MFT7570}}</td>
      <td class="riskCell">{{MFT7588}}</td>
      <td>{{MFT7589}}</td>
    </tr>
    <tr>
      <td class="titleRow">Delivery Channel</td>
      <td class="craCell">{{#if (eq MFT7569 "MORE15M")}}More than 15 months{{/if}}</td>
      <td class="riskCell">{{MFT7586}}</td>
      <td>{{MFT7587}}</td>
    </tr>
    <tr>
      <td class="titleRow">Length of Operation</td>
      <td class="craCell">{{#if (eq MFT7572 "MORE12M")}}More than 12 months{{/if}}</td>
      <td class="riskCell">{{MFT7590}}</td>
      <td>{{MFT7591}}</td>
    </tr>
    <tr>
      <td class="titleRow">Customer Geographical Connections</td>
      <td class="craCell">{{MFT7571}}</td>
      <td class="riskCell">{{MFT7584}}</td>
      <td>{{MFT7585}}</td>
    </tr>
    <tr>
      <td class="titleRow">Associated Geographical Connections</td>
      <td class="craCell">{{MFT7566}}</td>
      <td class="riskCell">{{MFT7582}}</td>
      <td>{{MFT7583}}</td>
    </tr>
    <tr>
      <td class="titleRow">ARI Auto High Risk</td>
      <td class="craCell">{{MFT7563}}</td>
      <td class="riskCell">{{MFT7576}}</td>
      <td>{{MFT7577}}</td>
    </tr>
    <tr>
      <td class="titleRow">ARI Refer Sanctions Risk Rating</td>
      <td class="craCell">{{MFT7564}}</td>
      <td class="riskCell">{{MFT7580}}</td>
      <td>{{MFT7581}}</td>
    </tr>
   <tr>
      <td class="titleRow">ARI Refer Prohibited Risk Rating</td>
      <td class="craCell">{{MFT7565}}</td>
      <td class="riskCell">{{MFT7578}}</td>
      <td>{{MFT7579}}</td>
    </tr>
  </table>
  <h2 class="subTitle">Additional Risk Indicators</h2>
  <table>
    <tr>
      <td class="subText" colspan="4">
        The additional risk indicators below were applied during remediation. If none show below, then none were applied.
      </td>
    </tr>
    <tr>
      <td class="titleColumn">ARI</td>
      <td class="titleColumn">Description</td>
      <td class="titleColumn">Value</td>
      <td class="titleColumn">Date Applied</td>
    </tr>
    {{#if (eq MFT6260 "yes")}}
    <tr>
      <td class="valueColumn">ARI001</td>
      <td class="valueColumn">Other Rationale For High Risk</td>
      <td class="valueColumn">{{MFT6260}}</td>

      <td class="valueColumn">{{date [MFT6260.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6313 "yes")}}
    <tr>
      <td class="valueColumn">ARI002</td>
      <td class="valueColumn">OBE Indicator</td>
      <td class="valueColumn">{{MFT6313}}</td>

      <td class="valueColumn">{{date [MFT6313.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6301 "yes")}}
    <tr>
      <td class="valueColumn">ARI003</td>
      <td class="valueColumn">Evasive Behaviour Indicator</td>
      <td class="valueColumn">{{MFT6301}}</td>

      <td class="valueColumn">{{date [MFT6301.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6246 "yes")}}
    <tr>
      <td class="valueColumn">ARI004</td>
      <td class="valueColumn">Anonymous Fictitious Account Indicator</td>
      <td class="valueColumn">{{MFT6246}}</td>

      <td class="valueColumn">{{date [MFT6246.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6237 "yes")}}
    <tr>
      <td class="valueColumn">ARI005</td>
      <td class="valueColumn">Cash Intensive Indicator Automated</td>
      <td class="valueColumn">{{MFT6237}}</td>

      <td class="valueColumn">{{date [MFT6237.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6284 "yes")}}
    <tr>
      <td class="valueColumn">ARI006</td>
      <td class="valueColumn">Customer False ID Indicator</td>
      <td class="valueColumn">{{MFT6284}}</td>

      <td class="valueColumn">{{date [MFT6284.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6277 "yes")}}
    <tr>
      <td class="valueColumn">ARI007</td>
      <td class="valueColumn">Legitimacy verification Additional Risk Indicator</td>
      <td class="valueColumn">{{MFT6277}}</td>

      <td class="valueColumn">{{date [MFT6277.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6219 "yes")}}
    <tr>
      <td class="valueColumn">ARI008</td>
      <td class="valueColumn">Customer refused information Indicator</td>
      <td class="valueColumn">{{MFT6219}}</td>

      <td class="valueColumn">{{date [MFT6219.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6248 "yes")}}
    <tr>
      <td class="valueColumn">ARI009</td>
      <td class="valueColumn">Shell Bank correspondent relationships Indicator</td>
      <td class="valueColumn">{{MFT6248}}</td>

      <td class="valueColumn">{{date [MFT6248.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6293 "yes")}}
    <tr>
      <td class="valueColumn">ARI010</td>
      <td class="valueColumn">Shell Bank Indicator</td>
      <td class="valueColumn">{{MFT6293}}</td>

      <td class="valueColumn">{{date [MFT6293.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6228 "yes")}}
    <tr>
      <td class="valueColumn">ARI011</td>
      <td class="valueColumn">Customer Relationship unclear indicator</td>
      <td class="valueColumn">{{MFT6228}}</td>

      <td class="valueColumn">{{date [MFT6228.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6302 "yes")}}
    <tr>
      <td class="valueColumn">ARI012</td>
      <td class="valueColumn">Foundation/Cultural Association Indicator</td>
      <td class="valueColumn">{{MFT6302}}</td>

      <td class="valueColumn">{{date [MFT6302.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6294 "yes")}}
    <tr>
      <td class="valueColumn">ARI013</td>
      <td class="valueColumn">Unregistered Charity Indicator</td>
      <td class="valueColumn">{{MFT6294}}</td>

      <td class="valueColumn">{{date [MFT6294.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6247 "yes")}}
    <tr>
      <td class="valueColumn">ARI014</td>
      <td class="valueColumn">Financial Crime Indicator</td>
      <td class="valueColumn">{{MFT6247}}</td>

      <td class="valueColumn">{{date [MFT6247.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6229 "yes")}}
    <tr>
      <td class="valueColumn">ARI015</td>
      <td class="valueColumn">External Investigation Indicator</td>
      <td class="valueColumn">{{MFT6229}}</td>

      <td class="valueColumn">{{date [MFT6229.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6285 "yes")}}
    <tr>
      <td class="valueColumn">ARI016</td>
      <td class="valueColumn">Criminal Activity Indicator</td>
      <td class="valueColumn">{{MFT6285}}</td>

      <td class="valueColumn">{{date [MFT6285.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6278 "yes")}}
    <tr>
      <td class="valueColumn">ARI017</td>
      <td class="valueColumn">Registered Jurisdiction Conflict Indicator</td>
      <td class="valueColumn">{{MFT6278}}</td>

      <td class="valueColumn">{{date [MFT6278.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6220 "yes")}}
    <tr>
      <td class="valueColumn">ARI018</td>
      <td class="valueColumn">Intermediary Entity with no Economic Activity Indicator</td>
      <td class="valueColumn">{{MFT6220}}</td>

      <td class="valueColumn">{{date [MFT6220.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6249 "yes")}}
    <tr>
      <td class="valueColumn">ARI019</td>
      <td class="valueColumn">High or Restricted Risk Jurisdiction Indicator (will affect all connected
        parties)</td>
      <td class="valueColumn">{{MFT6249}}</td>

      <td class="valueColumn">{{date [MFT6249.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6303 "yes")}}
    <tr>
      <td class="valueColumn">ARI020</td>
      <td class="valueColumn">HRTC Risk Jurisdiction Indicator (will affect all connected parties)</td>
      <td class="valueColumn">{{MFT6303}}</td>

      <td class="valueColumn">{{date [MFT6303.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6308 "yes")}}
    <tr>
      <td class="valueColumn">ARI021</td>
      <td class="valueColumn">Non-Resident Indicator</td>
      <td class="valueColumn">{{MFT6308}}</td>

      <td class="valueColumn">{{date [MFT6308.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6312 "yes")}}
    <tr>
      <td class="valueColumn">ARI022</td>
      <td class="valueColumn">Prohibited Jurisdiction Indicator</td>
      <td class="valueColumn">{{MFT6312}}</td>

      <td class="valueColumn">{{date [MFT6312.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6311 "yes")}}
    <tr>
      <td class="valueColumn">ARI023</td>
      <td class="valueColumn">Highly Restricted Jurisdiction Indicator (will affect all related parties)</td>
      <td class="valueColumn">{{MFT6311}}</td>

      <td class="valueColumn">{{date [MFT6311.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6306 "yes")}}
    <tr>
      <td class="valueColumn">ARI024</td>
      <td class="valueColumn">High Net Worth Individual Indicator</td>
      <td class="valueColumn">{{MFT6306}}</td>

      <td class="valueColumn">{{date [MFT6306.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6305 "yes")}}
    <tr>
      <td class="valueColumn">ARI025</td>
      <td class="valueColumn">Dual-use Goods Indicator</td>
      <td class="valueColumn">{{MFT6305}}</td>

      <td class="valueColumn">{{date [MFT6305.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6310 "yes")}}
    <tr>
      <td class="valueColumn">ARI026</td>
      <td class="valueColumn">Unauthorised or unlicensed gambling establishments Indicator</td>
      <td class="valueColumn">{{MFT6310}}</td>

      <td class="valueColumn">{{date [MFT6310.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6309 "yes")}}
    <tr>
      <td class="valueColumn">ARI027</td>
      <td class="valueColumn">Human rights abuse(s) Indicator</td>
      <td class="valueColumn">{{MFT6309}}</td>

      <td class="valueColumn">{{date [MFT6309.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6304 "yes")}}
    <tr>
      <td class="valueColumn">ARI028</td>
      <td class="valueColumn">HIGH RISK Licensed MSB / PSP Agent Indicator</td>
      <td class="valueColumn">{{MFT6304}}</td>

      <td class="valueColumn">{{date [MFT6304.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6307 "yes")}}
    <tr>
      <td class="valueColumn">ARI029</td>
      <td class="valueColumn">HIGH RISK Licensed MSB / PSP Indicator</td>
      <td class="valueColumn">{{MFT6307}}</td>

      <td class="valueColumn">{{date [MFT6307.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6295 "yes")}}
    <tr>
      <td class="valueColumn">ARI030</td>
      <td class="valueColumn">Prohibited NBFIs/MSBs/PSPs</td>
      <td class="valueColumn">{{MFT6295}}</td>

      <td class="valueColumn">{{date [MFT6295.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6297 "yes")}}
    <tr>
      <td class="valueColumn">ARI031</td>
      <td class="valueColumn">Nominee Shareholder Indicator</td>
      <td class="valueColumn">{{MFT6297}}</td>

      <td class="valueColumn">{{date [MFT6297.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6300 "yes")}}
    <tr>
      <td class="valueColumn">ARI032</td>
      <td class="valueColumn">Unidentifiable ownership Indicator</td>
      <td class="valueColumn">{{MFT6300}}</td>

      <td class="valueColumn">{{date [MFT6300.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6299 "yes")}}
    <tr>
      <td class="valueColumn">ARI033</td>
      <td class="valueColumn">Bearer Shares Indicator</td>
      <td class="valueColumn">{{MFT6299}}</td>

      <td class="valueColumn">{{date [MFT6299.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6298 "yes")}}
    <tr>
      <td class="valueColumn">ARI037</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 1 - Higher Risk) - Domestic</td>
      <td class="valueColumn">{{MFT6298}}</td>

      <td class="valueColumn">{{date [MFT6298.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6296 "yes")}}
    <tr>
      <td class="valueColumn">ARI038</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 2 - Higher Risk) - Domestic</td>
      <td class="valueColumn">{{MFT6296}}</td>

      <td class="valueColumn">{{date [MFT6296.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6238 "yes")}}
    <tr>
      <td class="valueColumn">ARI040</td>
      <td class="valueColumn">SAR Indicator</td>
      <td class="valueColumn">{{MFT6238}}</td>

      <td class="valueColumn">{{date [MFT6238.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6243 "yes")}}
    <tr>
      <td class="valueColumn">ARI041</td>
      <td class="valueColumn">Sanctions List Indicator</td>
      <td class="valueColumn">{{MFT6243}}</td>

      <td class="valueColumn">{{date [MFT6243.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6245 "yes")}}
    <tr>
      <td class="valueColumn">ARI043</td>
      <td class="valueColumn">SIE Indicator</td>
      <td class="valueColumn">{{MFT6245}}</td>

      <td class="valueColumn">{{date [MFT6245.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6241 "yes")}}
    <tr>
      <td class="valueColumn">ARI044</td>
      <td class="valueColumn">Tax Crime Indicator</td>
      <td class="valueColumn">{{MFT6241}}</td>

      <td class="valueColumn">{{date [MFT6241.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6240 "yes")}}
    <tr>
      <td class="valueColumn">ARI045</td>
      <td class="valueColumn">Higher Risk Transactions Indicator</td>
      <td class="valueColumn">{{MFT6240}}</td>

      <td class="valueColumn">{{date [MFT6240.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6244 "yes")}}
    <tr>
      <td class="valueColumn">ARI047</td>
      <td class="valueColumn">Virtual currency conversion Indicator</td>
      <td class="valueColumn">{{MFT6244}}</td>

      <td class="valueColumn">{{date [MFT6244.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6239 "yes")}}
    <tr>
      <td class="valueColumn">ARI048</td>
      <td class="valueColumn">Virtual currency activity Indicator - prohibited</td>
      <td class="valueColumn">{{MFT6239}}</td>

      <td class="valueColumn">{{date [MFT6239.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6242 "yes")}}
    <tr>
      <td class="valueColumn">ARI049</td>
      <td class="valueColumn">SIP/SIE Related Party Indicator</td>
      <td class="valueColumn">{{MFT6242}}</td>

      <td class="valueColumn">{{date [MFT6242.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6230 "yes")}}
    <tr>
      <td class="valueColumn">ARI050</td>
      <td class="valueColumn">Pornography Industry Indicator</td>
      <td class="valueColumn">{{MFT6230}}</td>

      <td class="valueColumn">{{date [MFT6230.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6234 "yes")}}
    <tr>
      <td class="valueColumn">ARI051</td>
      <td class="valueColumn">Entity with Residential Address Indicator</td>
      <td class="valueColumn">{{MFT6234}}</td>

      <td class="valueColumn">{{date [MFT6234.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6236 "yes")}}
    <tr>
      <td class="valueColumn">ARI052</td>
      <td class="valueColumn">IP Address Indicator</td>
      <td class="valueColumn">{{MFT6236}}</td>

      <td class="valueColumn">{{date [MFT6236.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6235 "yes")}}
    <tr>
      <td class="valueColumn">ARI053</td>
      <td class="valueColumn">Source of Funds Indicator</td>
      <td class="valueColumn">{{MFT6235}}</td>

      <td class="valueColumn">{{date [MFT6235.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6232 "yes")}}
    <tr>
      <td class="valueColumn">ARI054</td>
      <td class="valueColumn">Change to Ownership / Control / Nature of Business Indicator</td>
      <td class="valueColumn">{{MFT6232}}</td>

      <td class="valueColumn">{{date [MFT6232.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6231 "yes")}}
    <tr>
      <td class="valueColumn">ARI055</td>
      <td class="valueColumn">Adult Entertainment industry Indicator</td>
      <td class="valueColumn">{{MFT6231}}</td>

      <td class="valueColumn">{{date [MFT6231.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6233 "yes")}}
    <tr>
      <td class="valueColumn">ARI059</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 1 - Higher Risk) - International</td>
      <td class="valueColumn">{{MFT6233}}</td>

      <td class="valueColumn">{{date [MFT6233.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6286 "yes")}}
    <tr>
      <td class="valueColumn">ARI060</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 2 - Higher Risk) - International</td>
      <td class="valueColumn">{{MFT6286}}</td>

      <td class="valueColumn">{{date [MFT6286.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6291 "yes")}}
    <tr>
      <td class="valueColumn">ARI061</td>
      <td class="valueColumn">HIGH RISK NBFI</td>
      <td class="valueColumn">{{MFT6291}}</td>

      <td class="valueColumn">{{date [MFT6291.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6289 "yes")}}
    <tr>
      <td class="valueColumn">ARI064</td>
      <td class="valueColumn">ALL RISK LEVELS - NBFI/MSB/PSP</td>
      <td class="valueColumn">{{MFT6289}}</td>

      <td class="valueColumn">{{date [MFT6289.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6288 "yes")}}
    <tr>
      <td class="valueColumn">ARI065</td>
      <td class="valueColumn">Offshore License Indicator</td>
      <td class="valueColumn">{{MFT6288}}</td>

      <td class="valueColumn">{{date [MFT6288.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6287 "yes")}}
    <tr>
      <td class="valueColumn">ARI068</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 3 - Lower Risk PEP) - Domestic</td>
      <td class="valueColumn">{{MFT6287}}</td>

      <td class="valueColumn">{{date [MFT6287.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6290 "yes")}}
    <tr>
      <td class="valueColumn">ARI069</td>
      <td class="valueColumn">High Risk PEP Linked entity (Tier 3- Lower Risk PEP) - International</td>
      <td class="valueColumn">{{MFT6290}}</td>

      <td class="valueColumn">{{date [MFT6290.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6279 "yes")}}
    <tr>
      <td class="valueColumn">ARI070</td>
      <td class="valueColumn">Medium Risk PEP Linked entity (Tier 3 - Lower Risk PEP) - Domestic</td>
      <td class="valueColumn">{{MFT6279}}</td>

      <td class="valueColumn">{{date [MFT6279.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6282 "yes")}}
    <tr>
      <td class="valueColumn">ARI071</td>
      <td class="valueColumn">Medium Risk PEP Linked entity (Tier 3- Lower Risk PEP) - International</td>
      <td class="valueColumn">{{MFT6282}}</td>

      <td class="valueColumn">{{date [MFT6282.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6283 "yes")}}
    <tr>
      <td class="valueColumn">ARI077</td>
      <td class="valueColumn">Cash Intensive Indicator - Manual</td>
      <td class="valueColumn">{{MFT6283}}</td>

      <td class="valueColumn">{{date [MFT6283.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6280 "yes")}}
    <tr>
      <td class="valueColumn">ARI078</td>
      <td class="valueColumn">Complex Ownership / Control Indicator</td>
      <td class="valueColumn">{{MFT6280}}</td>

      <td class="valueColumn">{{date [MFT6280.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6281 "yes")}}
    <tr>
      <td class="valueColumn">ARI079</td>
      <td class="valueColumn">High risk Nature of Business/ Occupation</td>
      <td class="valueColumn">{{MFT6281}}</td>

      <td class="valueColumn">{{date [MFT6281.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6221 "yes")}}
    <tr>
      <td class="valueColumn">ARI080</td>
      <td class="valueColumn">Art market Indicator</td>
      <td class="valueColumn">{{MFT6221}}</td>

      <td class="valueColumn">{{date [MFT6221.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6225 "yes")}}
    <tr>
      <td class="valueColumn">ARI081</td>
      <td class="valueColumn">High-value dealer Indicator</td>
      <td class="valueColumn">{{MFT6225}}</td>

      <td class="valueColumn">{{date [MFT6225.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6227 "yes")}}
    <tr>
      <td class="valueColumn">ARI082</td>
      <td class="valueColumn">Court Order Unit Indicator</td>
      <td class="valueColumn">{{MFT6227}}</td>

      <td class="valueColumn">{{date [MFT6227.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6223 "yes")}}
    <tr>
      <td class="valueColumn">ARI085</td>
      <td class="valueColumn">Customer behaviour risk reduction indicator</td>
      <td class="valueColumn">{{MFT6223}}</td>

      <td class="valueColumn">{{date [MFT6223.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6222 "yes")}}
    <tr>
      <td class="valueColumn">ARI088</td>
      <td class="valueColumn">High or Restricted Risk Jurisdiction Indicator</td>
      <td class="valueColumn">{{MFT6222}}</td>

      <td class="valueColumn">{{date [MFT6222.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6224 "yes")}}
    <tr>
      <td class="valueColumn">ARI089</td>
      <td class="valueColumn">HRTC Risk Jurisdiction Indicator</td>
      <td class="valueColumn">{{MFT6224}}</td>

      <td class="valueColumn">{{date [MFT6224.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6250 "yes")}}
    <tr>
      <td class="valueColumn">ARI090</td>
      <td class="valueColumn">Prohibited Jurisdiction Indicator</td>
      <td class="valueColumn">{{MFT6250}}</td>

      <td class="valueColumn">{{date [MFT6250.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6255 "yes")}}
    <tr>
      <td class="valueColumn">ARI091</td>
      <td class="valueColumn">Highly Restricted Jurisdiction Indicator</td>
      <td class="valueColumn">{{MFT6255}}</td>

      <td class="valueColumn">{{date [MFT6255.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6259 "yes")}}
    <tr>
      <td class="valueColumn">ARI092</td>
      <td class="valueColumn">Alleged human rights abuse(s) Indicator</td>
      <td class="valueColumn">{{MFT6259}}</td>

      <td class="valueColumn">{{date [MFT6259.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6258 "yes")}}
    <tr>
      <td class="valueColumn">ARI093</td>
      <td class="valueColumn">Disqualified persons (Immigration Act 2014) Indicator</td>
      <td class="valueColumn">{{MFT6258}}</td>

      <td class="valueColumn">{{date [MFT6258.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6253 "yes")}}
    <tr>
      <td class="valueColumn">ARI094</td>
      <td class="valueColumn">Known illegal wildlife trading Indicator</td>
      <td class="valueColumn">{{MFT6253}}</td>

      <td class="valueColumn">{{date [MFT6253.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6252 "yes")}}
    <tr>
      <td class="valueColumn">ARI095</td>
      <td class="valueColumn">Alleged illegal wildlife trading Indicator</td>
      <td class="valueColumn">{{MFT6252}}</td>

      <td class="valueColumn">{{date [MFT6252.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6257 "yes")}}
    <tr>
      <td class="valueColumn">ARI096</td>
      <td class="valueColumn">Logging, timber tract operations OBE indicator</td>
      <td class="valueColumn">{{MFT6257}}</td>

      <td class="valueColumn">{{date [MFT6257.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6256 "yes")}}
    <tr>
      <td class="valueColumn">ARI097</td>
      <td class="valueColumn">Commercial fishing indicator</td>
      <td class="valueColumn">{{MFT6256}}</td>

      <td class="valueColumn">{{date [MFT6256.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6251 "yes")}}
    <tr>
      <td class="valueColumn">ARI098</td>
      <td class="valueColumn">Trapping and transport of live animals, furs, leathers, hides indicator</td>
      <td class="valueColumn">{{MFT6251}}</td>

      <td class="valueColumn">{{date [MFT6251.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6254 "yes")}}
    <tr>
      <td class="valueColumn">ARI099</td>
      <td class="valueColumn">Waste management Indicator</td>
      <td class="valueColumn">{{MFT6254}}</td>

      <td class="valueColumn">{{date [MFT6254.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6261 "yes")}}
    <tr>
      <td class="valueColumn">ARI0100</td>
      <td class="valueColumn">CBD/Medical cannabis Indicator</td>
      <td class="valueColumn">{{MFT6261}}</td>

      <td class="valueColumn">{{date [MFT6261.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6269 "yes")}}
    <tr>
      <td class="valueColumn">ARI0101</td>
      <td class="valueColumn">Customer holds a Tier one (investor) visa</td>
      <td class="valueColumn">{{MFT6269}}</td>

      <td class="valueColumn">{{date [MFT6269.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6276 "yes")}}
    <tr>
      <td class="valueColumn">ARI0102</td>
      <td class="valueColumn">Legal persons or arrangements that are personal asset-holding vehicles</td>
      <td class="valueColumn">{{MFT6276}}</td>

      <td class="valueColumn">{{date [MFT6276.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6274 "yes")}}
    <tr>
      <td class="valueColumn">ARI0103</td>
      <td class="valueColumn">Real estate (high risk) Indicator</td>
      <td class="valueColumn">{{MFT6274}}</td>

      <td class="valueColumn">{{date [MFT6274.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6266 "yes")}}
    <tr>
      <td class="valueColumn">ARI0104</td>
      <td class="valueColumn">The customer requests unnecessary or unreasonable levels of secrecy</td>
      <td class="valueColumn">{{MFT6266}}</td>

      <td class="valueColumn">{{date [MFT6266.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6265 "yes")}}
    <tr>
      <td class="valueColumn">ARI0105</td>
      <td class="valueColumn">Authorised or licensed gambling establishments Indicator</td>
      <td class="valueColumn">{{MFT6265}}</td>

      <td class="valueColumn">{{date [MFT6265.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6271 "yes")}}
    <tr>
      <td class="valueColumn">ARI0106</td>
      <td class="valueColumn">Pawnbroker Indicator</td>
      <td class="valueColumn">{{MFT6271}}</td>

      <td class="valueColumn">{{date [MFT6271.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6270 "yes")}}
    <tr>
      <td class="valueColumn">ARI0107</td>
      <td class="valueColumn">MTIC / Carousel fraud indicator</td>
      <td class="valueColumn">{{MFT6270}}</td>

      <td class="valueColumn">{{date [MFT6270.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6262 "yes")}}
    <tr>
      <td class="valueColumn">ARI0108</td>
      <td class="valueColumn">Relationships involving military, arms or defence related trade (where
      classified as defence) Indicator</td>
      <td class="valueColumn">{{MFT6262}}</td>

      <td class="valueColumn">{{date [MFT6262.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6268 "yes")}}
    <tr>
      <td class="valueColumn">ARI0109</td>
      <td class="valueColumn">Pawnbroker IndicatorHigh risk and Vostro correspondent relationship indicator</td>
      <td class="valueColumn">{{MFT6268}}</td>

      <td class="valueColumn">{{date [MFT6268.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6272 "yes")}}
    <tr>
      <td class="valueColumn">ARI0110</td>
      <td class="valueColumn">Hydroponic equipment sale indicator</td>
      <td class="valueColumn">{{MFT6272}}</td>

      <td class="valueColumn">{{date [MFT6272.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6263 "yes")}}
    <tr>
      <td class="valueColumn">ARI0111</td>
      <td class="valueColumn">Diplomatic Mission Accounts Indicator</td>
      <td class="valueColumn">{{MFT6263}}</td>

      <td class="valueColumn">{{date [MFT6263.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6275 "yes")}}
    <tr>
      <td class="valueColumn">ARI0112</td>
      <td class="valueColumn">Limited Partnership entities Indicator</td>
      <td class="valueColumn">{{MFT6275}}</td>

      <td class="valueColumn">{{date [MFT6275.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6273 "yes")}}
    <tr>
      <td class="valueColumn">ARI0113</td>
      <td class="valueColumn">Shelf company Indicator</td>
      <td class="valueColumn">{{MFT6273}}</td>

      <td class="valueColumn">{{date [MFT6273.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6267 "yes")}}
    <tr>
      <td class="valueColumn">ARI0114</td>
      <td class="valueColumn">Shell company Indicator</td>
      <td class="valueColumn">{{MFT6267}}</td>

      <td class="valueColumn">{{date [MFT6267.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
    {{#if (eq MFT6264 "yes")}}
    <tr>
      <td class="valueColumn">ARI0115</td>
      <td class="valueColumn">Source of funds or wealth cannot be easily explained Indicator</td>
      <td class="valueColumn">{{MFT6264}}</td>

      <td class="valueColumn">{{date [MFT6264.valueUpdatedTimestamp] "DD/MM/YYYY"}}</td>

    </tr>
    {{/if}}
  </table>
</div>
