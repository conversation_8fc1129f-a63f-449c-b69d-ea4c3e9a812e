<style type="text/css">
  @page {
    size: {{size}};
    margin: 1em;
  }

  html {
    margin: 0;
    padding: 0;
    background-color: white;
  }

  body {
    margin: 0;
    padding: 0;
    counter-reset: page;
    /* Paper sizes from https://en.wikipedia.org/wiki/Paper_size#Overview_of_ISO_paper_sizes */
    {{#if (eq size "A3 landscape")}}
    width: 420mm;
    {{else if (or (eq size "A3 portrait") (eq size "A4 landscape"))}}
    width: 297mm;
    {{else if (or (eq size "A4 portrait") (eq size "A5 landscape"))}}
    width: 210mm;
    {{else if (eq size "A5 portrait")}}
    width: 148mm;
    {{/if}}

    @media print {
      width: unset;
    }
  }

  /* A section represents a page when converted to PDF or printed */
  section {
    counter-increment: page;
    page-break-after: always;
    break-after: page;
    margin: 1em;

    @media print {
      margin: unset;
    }
  }

  div.page-break {
    counter-increment: page;
    page-break-before: always;
    break-before: page;
  }

  tr {
    page-break-inside: avoid;
  }

  div.page-number {
    text-align: right;

    &:before {
      content: "{{#if (or pre-page-number (eq pre-page-number ""))}}{{pre-page-number}}{{else}}Page{{/if}} " counter(page);
    }
  }

  {{#if _dev_}}
  /* Purely for DX when rendering as HTML to get a rough idea of PDF rendering */
  section {
    border-bottom: 1px solid black;
    @media print {
      border-bottom: none;
    }
  }
  div.page-break {
    border-top: 1px solid black;
    @media print {
      border-top: none;
    }
  }
  {{/if}}
</style>
