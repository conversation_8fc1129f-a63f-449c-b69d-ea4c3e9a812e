<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:x="urn:schemas-microsoft-com:office:excel"
  xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
  xmlns:html="http://www.w3.org/TR/REC-html40">
  <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
    <Author>CDD Remediation MI</Author>
    <LastAuthor>CDD Remediation MI</LastAuthor>
    <Created>2023-10-12T09:23:37Z</Created>
    <LastSaved>2023-10-23T09:27:13Z</LastSaved>
    <Version>16.00</Version>
  </DocumentProperties>
  <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
    <AllowPNG />
  </OfficeDocumentSettings>
  <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
    <WindowHeight>15440</WindowHeight>
    <WindowWidth>26440</WindowWidth>
    <WindowTopX>5580</WindowTopX>
    <WindowTopY>4680</WindowTopY>
    <ProtectStructure>False</ProtectStructure>
    <ProtectWindows>False</ProtectWindows>
  </ExcelWorkbook>
  <Styles>
    <Style ss:ID="Default" ss:Name="Normal">
      <Alignment ss:Vertical="Bottom" />
      <Borders />
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
      <Interior />
      <NumberFormat />
      <Protection />
    </Style>
    <Style ss:ID="s62">
      <Alignment ss:Vertical="Top" />
    </Style>
    <Style ss:ID="s63">
      <Alignment ss:Horizontal="Center" ss:Vertical="Top" />
    </Style>
    <Style ss:ID="s64">
      <Alignment ss:Vertical="Top" ss:WrapText="1" />
    </Style>
    <Style ss:ID="s65">
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="14" ss:Bold="1" />
    </Style>
    <Style ss:ID="s67">
      <Font ss:FontName="Calibri" x:Family="Swiss" />
    </Style>
    <Style ss:ID="s68">
      <Alignment ss:Vertical="Top" />
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#FF0000" />
    </Style>
    <Style ss:ID="s69">
      <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1" />
      <Borders>
        <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" />
      </Borders>
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
        ss:Bold="1" />
      <Interior ss:Color="#000080" ss:Pattern="Solid" />
    </Style>
    <Style ss:ID="s74">
      <Alignment ss:Horizontal="Center" ss:Vertical="Top" />
      <Borders>
        <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" />
      </Borders>
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
    </Style>
    <Style ss:ID="s75">
      <Alignment ss:Horizontal="Center" ss:Vertical="Top" />
      <Borders>
        <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" />
      </Borders>
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
      <NumberFormat ss:Format="Short Date" />
    </Style>
    <Style ss:ID="s76">
      <Alignment ss:Horizontal="Center" ss:Vertical="Top" />
      <Borders>
        <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
          ss:Color="#000000" />
        <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
          ss:Color="#000000" />
        <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
          ss:Color="#000000" />
        <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
          ss:Color="#000000" />
      </Borders>
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
      <NumberFormat ss:Format="Short Date" />
    </Style>
    <Style ss:ID="s77">
      <Alignment ss:Horizontal="Center" ss:Vertical="Top" ss:WrapText="1" />
      <Borders>
        <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" />
        <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" />
      </Borders>
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
      <NumberFormat ss:Format="Short Date" />
    </Style>
    <Style ss:ID="s78">
      <Alignment ss:Vertical="Top" />
      <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000" />
    </Style>
  </Styles>
  <Worksheet ss:Name="RD Dashboard">
    <Table ss:ExpandedColumnCount="36" ss:ExpandedRowCount="{{row_count}}" x:FullColumns="1"
      x:FullRows="1" ss:StyleID="s62" ss:DefaultColumnWidth="53"
      ss:DefaultRowHeight="15">
      <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="289" />
   <Column ss:StyleID="s62"
        ss:AutoFitWidth="0" ss:Width="396" />
   <Column ss:StyleID="s62" ss:AutoFitWidth="0"
        ss:Width="304" />
   <Column ss:StyleID="s62" ss:Width="48" />
   <Column ss:StyleID="s62"
        ss:Width="73" />
   <Column ss:StyleID="s62" ss:Width="90" />
   <Column ss:StyleID="s62"
        ss:AutoFitWidth="0" ss:Width="139" />
   <Column ss:StyleID="s62" ss:Width="57" />
   <Column
        ss:StyleID="s62" ss:Width="311" />
   <Column ss:StyleID="s62" ss:Width="105" />
   <Column
        ss:StyleID="s62" ss:Width="141" />
   <Column ss:StyleID="s62" ss:Width="131" />
   <Column
        ss:StyleID="s63" ss:AutoFitWidth="0" ss:Width="108" />
   <Column ss:StyleID="s63"
        ss:AutoFitWidth="0" ss:Width="99" />
   <Column ss:StyleID="s63" ss:Width="137" />
   <Column
        ss:StyleID="s63" ss:AutoFitWidth="0" ss:Width="80" />
   <Column ss:StyleID="s63"
        ss:AutoFitWidth="0" ss:Width="94" />
   <Column ss:StyleID="s64" ss:AutoFitWidth="0"
        ss:Width="79" />
   <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="172" />
   <Column
        ss:StyleID="s64" ss:Width="78" />
   <Column ss:StyleID="s62" ss:Width="84" />
   <Column
        ss:StyleID="s62" ss:Width="115" ss:Span="3" />
   <Column ss:Index="26" ss:StyleID="s62"
        ss:Width="177" />
   <Column ss:StyleID="s62" ss:Width="83" />
   <Column ss:StyleID="s62"
        ss:Width="175" />
   <Column ss:StyleID="s62" ss:Width="172" />
   <Column ss:StyleID="s62"
        ss:Width="239" />
   <Column ss:StyleID="s62" ss:Width="185" />
   <Column ss:StyleID="s62"
        ss:Width="83" />
   <Column ss:StyleID="s62" ss:Width="89" />
   <Column ss:StyleID="s62"
        ss:Width="110" />
   <Column ss:StyleID="s62" ss:Width="99" />
   <Row ss:AutoFitHeight="0"
        ss:Height="19">
        <Cell ss:StyleID="s65">
          <Data ss:Type="String">{{dashboard_type}}</Data>
        </Cell>
      </Row>
   <Row
        ss:AutoFitHeight="0">
        <Cell ss:StyleID="Default">
          <ss:Data ss:Type="String"
            xmlns="http://www.w3.org/TR/REC-html40">
            <Font html:Color="#000000">As at: </Font>
            <Font
              html:Color="#000000">{{./date}}</Font>
          </ss:Data>
        </Cell>
      </Row>
   <Row
        ss:AutoFitHeight="0">
        <Cell ss:StyleID="s67">
          <Data ss:Type="String">Santander Confidential, Remediation Dataset</Data>
        </Cell>
        <Cell ss:StyleID="s68" />
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="48">
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">RD Name</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Delegate</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">RD Region</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Case ID</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">J-Number</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer name</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Division</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Family ID</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Status</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">RD Action Required</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Outreach Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Oldest O/S Requirement</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">FO Portal Submission Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Verification Form Submitted</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Documents Outstanding</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Documents outstanding &gt;=30 Days</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Last Document Upload Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Portal Resubmit Outstanding</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Outbound Call Outstanding</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Next Blocking Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Blocking Status</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Blocking Status Change</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Days until Blocking Instruction Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Blocking Instruction Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Date of last inbound call to KPMG helpline</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Date of last outbound from KPMG helpline</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Portfolio Code</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Contact Name</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Contact Email</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Contact Telephone Number</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Customer Contact Address</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Analyst Name</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Population flag</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Non Standard Journey Flag </Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">Last Comms Date</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
        <Cell ss:StyleID="s69">
          <Data ss:Type="String">EDD Flag</Data>
          <NamedCell
            ss:Name="_FilterDatabase" />
        </Cell>
      </Row> 
      {{#each rd_data}} 
      <Row ss:AutoFitHeight="0">
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">
            {{rd_name}}
          </Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">
            {{rd_delegate}}
          </Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">
            {{rd_region}}
          </Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{case_id}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{jnumber}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{customer_name}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{division}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{family_id}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">
            {{customer_status}}
          </Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{rd_action_required}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{customer_outreach_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{oldest_os_requirement}}</Data>
        </Cell>
        <Cell ss:StyleID="s76">
          <Data ss:Type="String">{{fo_portal_submission_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{customer_verification_form_submitted}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{documents_outstanding}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{documents_outstanding_30_days}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{last_doc_upload_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s77">
          <Data ss:Type="String">{{portal_resubmit_outstading}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{outbound_ccc_outstanding}}</Data>
        </Cell>
        <Cell ss:StyleID="s77">
          <Data ss:Type="String">{{next_blocking_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{blocking_status}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{blocking_status_change}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{days_until_blocking_instruction}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{blocking_instruction_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{date_last_inbound_call}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{date_last_outbound_call}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{portfolio_code}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{customer_contact_name}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{customer_contact_email}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{customer_contact_phone_number}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{customer_contact_address}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{analyst_name}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{population_flag}}</Data>
        </Cell>
        <Cell ss:StyleID="s74">
          <Data ss:Type="String">{{non_std_journey_flag}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{last_comms_date}}</Data>
        </Cell>
        <Cell ss:StyleID="s75">
          <Data ss:Type="String">{{edd_flag}}</Data>
        </Cell>
      </Row> 
      {{/each}} 
    </Table>
    <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
      <PageSetup>
        <Header x:Margin="0.3" />
        <Footer x:Margin="0.3" />
        <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75" />
      </PageSetup>
      <Unsynced />
      <Print>
        <ValidPrinterInfo />
        <HorizontalResolution>600</HorizontalResolution>
        <VerticalResolution>600</VerticalResolution>
      </Print>
      <Zoom>125</Zoom>
      <Selected />
      <DoNotDisplayGridlines />
      <FreezePanes />
      <FrozenNoSplit />
      <SplitHorizontal>4</SplitHorizontal>
      <TopRowBottomPane>4</TopRowBottomPane>
      <ActivePane>2</ActivePane>
      <Panes>
        <Pane>
          <Number>3</Number>
        </Pane>
        <Pane>
          <Number>2</Number>
          <ActiveRow>3</ActiveRow>
        </Pane>
      </Panes>
      <ProtectObjects>False</ProtectObjects>
      <ProtectScenarios>False</ProtectScenarios>
    </WorksheetOptions>
    <AutoFilter x:Range="R4C1:R9C36"
      xmlns="urn:schemas-microsoft-com:office:excel">
    </AutoFilter>
  </Worksheet>
</Workbook>