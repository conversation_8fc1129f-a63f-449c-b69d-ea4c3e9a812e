{"type": "object", "required": ["dashboard_type", "date", "row_count", "rd_data"], "properties": {"rd_data": {"type": "array", "items": {"type": "object", "required": ["rd_name", "rd_delegate", "rd_region", "case_id", "jnumber", "customer_name", "division", "family_id", "customer_status", "rd_action_required", "customer_outreach_date", "oldest_os_requirement", "fo_portal_submission_date", "customer_verification_form_submitted", "documents_outstanding", "documents_outstanding_30_days", "last_doc_upload_date", "portal_resubmit_outstanding", "outbound_ccc_outstanding", "next_blocking_date", "blocking_status", "blocking_status_change", "days_until_blocking_instruction", "blocking_instruction_date", "date_last_inbound_call", "date_last_outbound_call", "portfolio_code", "customer_contact_name", "customer_contact_email", "customer_contact_phone_number", "customer_contact_address", "analyst_name", "population_flag", "non_std_journey_flag", "last_comms_date", "edd_flag"]}}}}