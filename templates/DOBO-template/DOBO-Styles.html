<style type="text/css">
  * {
    font-family: sans-serif;
    font-size: 12px;
    line-height: 1.5;
    text-align: left;
  }

  section {
    margin-top: 30px;
  }

  .header {
    background-color: #CC0000;
    font-size: 18px;
    color: white;
    padding: 5px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 50px;

    &:last-of-type {
      margin-bottom: 0;
    }

    &.addInfo {
      width: 50%;
    }

    &.nominees {
      table-layout: auto;

      td:last-child {
        width: 70%;
        word-break: break-all;
      }
    }
  }

  thead {
    background-color: #F7C7AC;
  }

  th,
  td {
    border: 1px solid black;
    overflow-wrap: break-word;
    padding: 5px;

    &.fixed {
      width: 100px;
    }
  }

  .bold {
    font-weight: bold;
  }

  .list {
    &::after {
      content: ",";
    }

    &:last-child::after {
      content: none;
    }
  }
</style>
