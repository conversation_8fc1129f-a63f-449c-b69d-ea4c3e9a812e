<!DOCTYPE document SYSTEM "rml_1_0.dtd">
<document filename="T0 example.pdf">
    <docinit>
        <registerTTFont faceName="RNHouseSansW01Bold" fileName="{{contentHost}}/santander/fonts/SantanderHeadline-Bold.ttf" />
        <registerTTFont faceName="RNHouseSansW01Regular" fileName="{{contentHost}}/santander/fonts/SantanderText-Regular.ttf" />
    </docinit>
    <template pageSize="(210, 297)">
        <pageTemplate id="main">
            <pageGraphics>
                <image file="{{contentHost}}/santander/emails/san-logo.png" x="2.54cm" y="26.5cm" width="5cm" height="0.89cm"/>
            <rotate degrees="-90" />
                <barCode x="-26.5cm" y="19.5cm" code="Code128" barWidth="0.0275cm" barHeight="1cm">{{caseId}}</barCode>
            </pageGraphics>
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main2">
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main3">
            <pageGraphics>
            <rotate degrees="-90" />
                <barCode x="-26.5cm" y="19.5cm" code="Code128" barWidth="0.0275cm" barHeight="1cm">{{caseId}}</barCode>
            </pageGraphics>
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main4">
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
    </template>
    {{> letterStyle}}
    <story>
        {{> custContact}}
        <storyPlace x="2.54cm" y="4.3cm" width="15.99cm" height="15.5cm" origin="page"> 
            <para style="salutation">Dear {{salutationName}},</para>
            <para style="h1"> <br /><b>Customer Verification - we need you to resubmit some documents</b> </para>
            <para style="normal">
                <br />
                Thank you for providing us your documents – we appreciate this. Unfortunately, some documents were either not valid or the certification does not meet our requirements, so we’ll need you to make some changes and resubmit them.  
            </para>
            <para style="normal">
                <br />
                We appreciate this is frustrating, and we apologise for the inconvenience. 
            </para>
            <para style="h3"><br /><br /><b>What you need to do</b></para>
            <para style="normal">
                <br />
                The table on the following page shows the documents that we were not able to accept and the reason why. You can also find this information on the Customer Verification portal.<br />
            </para>
            <para style="normal">
                <br />
                Please can you look at the table, make the required changes and resubmit the documents.
            </para>
            <para style="normal">
                <br />
                If the document has an * at the end, this document needs to be certified and meet specific certification requirements (details attached). 
            </para>
            <para style="normal">
                <br />
                {{> whatYouNeedToDo}}
            </para>
            <para style="normal">
                <br />
                If you are unable to upload your documents onto the Customer Verification portal, we have included a pre-paid envelope for you to send us your documents. If using this method, please include the return slip attached.
            </para>
            <para style="h3"><br /><br /><b>We're here to help</b></para>
            <para style ="normal">
                <br />
                If you require some help to upload your documents or need any other assistance, give us a call on {{> sanOperationalNum}}. We’re here between 9am and 5pm from Monday to Friday (excluding UK public holidays). When calling us you may wish to avoid peak periods such as lunchtimes when lines can be busier and call times a little longer.
            </para>
            <para style="normal">
                <br />
                We’ve enclosed our Frequently Asked Questions to help you.
            </para>
            <para style="normal"><br /><br />We appreciate your help in fighting financial crime.</para>
        </storyPlace>
        {{> footer}}
        <setNextTemplate name="main2" />
        <nextPage />
        <storyPlace x="2.54cm" y="11.8cm" width="15.99cm" height="15.5cm" origin="page">
            <para style="normal"><br /><br />Yours sincerely,</para>
            <para style="normal"><br />The Santander Customer Verification Team</para>
        </storyPlace>
        <storyPlace x="2.54cm" y="20cm" width="15.99cm" height="27.5cm" origin="page"> 
        <storyPlace x="2.54cm" y="13.5cm" width="15.98cm" height="10.5cm" origin="page">
        {{> letterSecurity}}
        {{> footer}}
        <setNextTemplate name="main2" />
        <nextPage />
        <storyPlace x="2.54cm" y="3.5cm" width="15.99cm" height="25cm" origin="page">
            <para style="FAQheader">Documents that require resubmission</para>
             <spacer length="0.5cm"/>
            <blockTable style="table2">
                <tr>
                    <td><para style="normalbold">Document</para></td>
                    <td><para style="normalbold">Client / Key Individual</para></td>
                    <td><para style="normalbold">Reason we can’t accept the document</para></td>
                </tr>
                {{#each documents}}
                <tr>
                    <td><para style="normal">{{document}}</para></td>
                    <td><para style="normal">{{customer}}</para></td>
                    <td>
                        <ol style="list_normal">
                            {{#each reason}}
                                <li><para style="list_text">{{reason}}</para></li>
                            {{/each}}
                        </ol>
                    </td>
                </tr>
                {{/each}}
		    </blockTable>
          {{> footer}}
        </storyPlace>
        <nextPage />
        {{> FAQ}}
        {{> footer}}
        <condPageBreak height="10in"/>
        <setNextTemplate name="main3" />
        <nextPage />
        {{> REPLY-SLIP}}
        {{> footer}}
        <setNextTemplate name="main4"/>
        <nextPage/>
        <setNextTemplate name="main2" />
        <nextPage />
        {{> CERT-REQ}}
        {{> footer}}   
    </story>
</document>
