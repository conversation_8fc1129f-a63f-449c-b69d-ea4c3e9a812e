<!DOCTYPE document SYSTEM "rml_1_0.dtd">
<document filename="T0 example.pdf">
    <docinit>
        <registerTTFont faceName="RNHouseSansW01Bold" fileName="{{contentHost}}/santander/fonts/SantanderHeadline-Bold.ttf" />
        <registerTTFont faceName="RNHouseSansW01Regular" fileName="{{contentHost}}/santander/fonts/SantanderText-Regular.ttf" />
    </docinit>
    <template pageSize="(210, 297)">
        <pageTemplate id="main">
            <pageGraphics>
                <image file="{{contentHost}}/santander/emails/san-logo.png" x="2.54cm" y="26.5cm" width="5cm" height="0.89cm"/>
            <rotate degrees="-90" />
                <barCode x="-26.5cm" y="19.5cm" code="Code128" barWidth="0.0275cm" barHeight="1cm">{{taskId}}</barCode>
            </pageGraphics>
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main2">
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main3">
            <pageGraphics>
            <rotate degrees="-90" />
                <barCode x="-26.5cm" y="19.5cm" code="Code128" barWidth="0.0275cm" barHeight="1cm">{{taskId}}</barCode>
            </pageGraphics>
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
        <pageTemplate id="main4">
            <frame id="first" x1="2.54cm" y1="1.5cm" height="20.98cm" width="15.99cm" />
        </pageTemplate>
    </template>
    {{> oddLetterStyle}}
    <story>
        {{> oddCustContact}}
        <storyPlace x="2.54cm" y="4.3cm" width="15.99cm" height="15.5cm" origin="page"> 
            <para style="salutation">Dear {{salutationName}},</para>
            <para style="h1"> <br /><b>
            {{> oddIDNoopsLetter}}
            </b> </para>
            <para style="normal">
                <br />
                Thank you for providing us your documents – we appreciate this. Unfortunately, some documents were either not valid or the certification does not meet our requirements, so we’ll need you to make some changes and resubmit them.  
            </para>
            <para style="normal">
                <br />
               If you have any queries, please contact your relationship director or the customer helpline on {{> oddSanOperationalNum}}.  
            </para>
            <para style="h3"><br /><br /><b>What you need to do</b></para>
            <para style="normal">
                <br />
                The table on the following page shows the documents that we were not able to accept and the reason why. You can also find this information on the Customer Verification portal.<br />
            </para>
            <para style="normal">
                <br />
                Please make the necessary changes and resubmit the documents on the portal at {{> oddSanPortalDomainLetter}}. Alternatively, you can visit the Santander Corporate Banking website and click "Log On" to the Customer Verification link in the drop down. You can use <span style="normalbold">{{username}}</span> as your username.
            </para>
            <para style="normal">
                <br />
                If you are unable to upload your documents onto the Customer Verification portal, we have included a pre-paid envelope for you to send us your documents. If using this method, please include the return slip attached.
            </para>
            
            {{#if (eq noOpsStatus "Off")}}
            <spacer length="0.3cm"/>
            <para style="h3"><br /><b>
            If we don't hear back from you
            </b></para> 
            {{/if}}
            
            <para style ="normal"><br />
            {{#if (eq noOpsStatus "Off")}}
            If we don't receive this information by 
            {{/if}}
            
            
            <span style="normalbold">{{> oddNoopsRestriction}}</span>
            {{> oddNoopsRestrictionDeadline}}
            </para>
            <spacer length="0.55cm"/>
            <para style="h3"><b>We're here to help</b></para>
            <para style ="normal">
                <br />
               If you need any help, please call the helpline on {{> oddSanOperationalNum}}. We’re here between 9am and 5pm from Monday to Friday (excluding UK public holidays). When calling us you may wish to avoid peak periods such as lunchtimes when lines can be busier and call times a little longer.
            </para>
            <spacer length="0.2cm"/>
            <para style="normal">We appreciate your help in fighting financial crime.</para>
            <para style="normal"><br />Yours sincerely,</para>
            <spacer length="0.2cm"/>
            <para style="normal">The Santander Customer Verification Team</para>
        
        </storyPlace>
        {{> oddFooter}}
        <setNextTemplate name="main2" />
        
        <setNextTemplate name="main2" />
        <nextPage />
        <storyPlace x="2.54cm" y="3.5cm" width="15.99cm" height="25cm" origin="page">
            <spacer length="1cm"/>
            <para style="FAQheader">Documents that require resubmission</para>
             <spacer length="0.5cm"/>
             <para style="normal">If the document has an * at the end, this document needs to be certified and meet specific certification requirements (details attached).</para>
            <spacer length="0.5cm"/>
            <blockTable style="table2" colWidths="2.5cm,3.5cm,9.5cm">
                <tr>
                    <td><para style="normalbold">Document</para></td>
                    <td><para style="normalbold">Client / Key Individual</para></td>
                    <td><para style="normalbold">Reason we can’t accept the document</para></td>
                </tr>
                {{#each documents}}
                <tr>
                    <td><para style="normal">{{document}}</para></td>
                    <td><para style="normal">{{customer}}</para></td>
                    <td>
                        <ol style="list_normal">
                            {{#each reason}}
                                <li><para style="list_text">{{reason}}</para></li>
                            {{/each}}
                        </ol>
                    </td>
                </tr>
                {{/each}}
		    </blockTable>
          <!--{{> footer}} -->
          {{> oddFooter}}
        </storyPlace>
        <nextPage />
        {{> oddFAQ}}
        {{> oddFooter}}
        <condPageBreak height="10in"/>
        <setNextTemplate name="main3" />
        <nextPage />
        {{> oddREPLY-SLIP}}
        {{> oddFooter}}
        <setNextTemplate name="main4"/>
        <nextPage/>
        <setNextTemplate name="main2" />
        <nextPage />
        {{> oddCERT-REQ}}
        {{> oddFooter}}   
    </story>
</document>
