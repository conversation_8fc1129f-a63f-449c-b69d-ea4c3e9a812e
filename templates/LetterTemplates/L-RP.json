{"type": "object", "properties": {"contentHost": {"type": "string", "minLength": 1}, "contactName": {"type": "string", "minLength": 1}, "fullLegalEntityName": {"type": "string", "minLength": 1}, "salutationName": {"type": "string", "minLength": 1}, "username": {"type": "string", "minLength": 1}, "address1": {"type": "string", "minLength": 1}, "country": {"type": "string", "minLength": 1}, "caseId": {"type": "string", "minLength": 1}, "letterType": {"type": "string", "minLength": 1}, "letterIssuedDate": {"type": "string", "minLength": 1}, "restrictionDeadline": {"type": "string", "minLength": 1}, "franchise": {"type": "string", "minLength": 1}}, "required": ["contentHost", "contactName", "fullLegalEntityName", "salutationName", "username", "address1", "country", "caseId", "letterType", "letterIssuedDate", "restrictionDeadline", "franchise"]}